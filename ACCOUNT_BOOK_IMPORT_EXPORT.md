# 账本导入导出功能说明

## 功能概述

现在账单导出和导入功能已经支持账本数据的处理，包括：

1. **导出时包含账本信息**：导出的CSV文件现在包含账本ID和账本名称
2. **导入时自动创建账本**：如果导入数据中包含不存在的账本，系统会自动创建
3. **智能账本匹配**：导入时会检查本地是否已存在同名账本，避免重复创建

## 导出格式

导出的CSV文件现在包含以下字段：
```
日期,类型,金额,分类,家庭账单id,家庭账单,账本id,账本,备注
```

示例：
```csv
日期,类型,金额,分类,家庭账单id,家庭账单,账本id,账本,备注
2024-01-01,支出,100,餐饮,,,1,默认账本,"午餐"
2024-01-02,收入,5000,工资,,,2,工作账本,"月薪"
```

## 导入逻辑

### 账本处理流程

1. **扫描导入数据**：系统首先扫描所有待导入的交易记录，收集其中的账本信息
2. **检查现有账本**：对于每个账本名称，检查本地数据库是否已存在同名账本
3. **自动创建账本**：如果账本不存在，系统会自动创建新账本
4. **关联交易记录**：导入交易记录时，自动关联到正确的账本

### 账本匹配规则

- **按名称匹配**：优先根据账本名称进行匹配
- **默认账本处理**：如果没有指定账本或账本名称为"默认账本"，则使用系统默认账本
- **ID兼容性**：支持原有的账本ID导入方式，保持向后兼容

## 新增的数据库方法

### DatabaseService 新增方法

1. **getAccountBookByName(name: string)**
   - 根据账本名称查找账本
   - 返回匹配的账本对象或null

2. **createAccountBookIfNotExists(name: string, description?: string)**
   - 创建账本（如果不存在）
   - 如果同名账本已存在，返回现有账本
   - 如果不存在，创建新账本并返回

## 使用示例

### 导入包含账本信息的数据

```javascript
// 示例导入数据
const importData = {
  transactions: [
    {
      date: '2024-01-01',
      type: 'expense',
      amount: '100',
      category: '餐饮',
      note: '午餐',
      accountBookName: '生活账本'  // 如果不存在会自动创建
    },
    {
      date: '2024-01-02',
      type: 'income',
      amount: '5000',
      category: '工资',
      note: '月薪',
      accountBookName: '工作账本'  // 如果不存在会自动创建
    }
  ]
};
```

### 导入结果

- 系统会自动创建"生活账本"和"工作账本"（如果不存在）
- 交易记录会正确关联到对应的账本
- 导入完成后会显示创建的账本数量

## 注意事项

1. **账本名称唯一性**：系统根据账本名称判断是否为同一账本
2. **默认账本保护**：系统默认账本不会被删除或重复创建
3. **向后兼容**：原有的导入数据格式仍然支持
4. **错误处理**：如果账本创建失败，交易记录会关联到默认账本

## 测试建议

1. **测试自动创建**：导入包含新账本名称的数据，验证账本是否正确创建
2. **测试重复导入**：多次导入相同账本名称的数据，验证不会重复创建账本
3. **测试混合数据**：导入包含现有账本和新账本的混合数据
4. **测试导出导入循环**：导出数据后重新导入，验证数据完整性
