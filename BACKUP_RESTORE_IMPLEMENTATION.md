# 其他数据备份/恢复功能实现

## 🎯 功能分离

### 账单数据 vs 其他数据

| 功能 | 账单数据 | 其他数据 |
|------|----------|----------|
| **数据类型** | 交易记录、分类、账本 | 信用卡、分期账单、借款记录、愿望清单 |
| **导出功能** | "导出账单" | "数据备份" |
| **导入功能** | "导入账单" | "数据恢复" |
| **文件格式** | CSV/JSON | JSON |
| **文件选择函数** | `getFileFromDocuments()` | `getBackupFileFromDocuments()` |
| **文件分享函数** | `shareFile()` | `shareBackupFile()` |

## 🔧 技术实现

### 1. 专门的文件分享函数

```javascript
// src/utils/utils.js

// 分享CSV文件（专门用于账单数据）
export const shareFile = async (content, fileName) => {
  try {
    const downloadsPath = RNFS.DownloadDirectoryPath;
    const filePath = `${downloadsPath}/${fileName}_${dayjs().format(
      'YYYY-MM-DD',
    )}-${new Date().getTime()}.csv`;

    await RNFS.writeFile(filePath, content, 'utf8');
    await Share.open({
      url: `file://${filePath}`,
      type: 'text/csv',
      filename: fileName,
      failOnCancel: false,
    });
  } catch (error) {
    Alert.alert('分享失败', error.message);
    throw error;
  }
};

// 分享JSON备份文件（专门用于其他数据备份）
export const shareBackupFile = async (content, fileName) => {
  try {
    // 确保文件名包含时间戳
    const finalFileName = fileName.includes('_') ? fileName :
      `${fileName}_${dayjs().format('YYYY-MM-DD')}-${new Date().getTime()}`;

    // 确保文件扩展名是 .json
    const jsonFileName = finalFileName.endsWith('.json') ? finalFileName : `${finalFileName}.json`;

    // 文件路径
    const downloadsPath = RNFS.DownloadDirectoryPath;
    const filePath = `${downloadsPath}/${jsonFileName}`;

    // 写入JSON文件
    await RNFS.writeFile(filePath, content, 'utf8');

    // 分享文件
    await Share.open({
      url: `file://${filePath}`,
      type: 'application/json',
      filename: jsonFileName,
      failOnCancel: false,
    });
  } catch (error) {
    Alert.alert('备份分享失败', error.message);
    throw error;
  }
};
```

### 2. 专门的文件选择函数

```javascript
// src/utils/utils.js
export const getBackupFileFromDocuments = async () => {
  try {
    const [pickResult] = await pick();
    const {uri, name} = pickResult;

    // 文件类型验证
    if (!name.endsWith('.json')) {
      return { error: '请选择JSON格式的备份文件' };
    }

    // 文件名验证
    if (!name.includes('其他数据备份') && !name.includes('雪球记账')) {
      return { error: '请选择雪球记账的其他数据备份文件' };
    }

    // 读取和解析文件
    const fileContent = await RNFS.readFile(uri, 'utf8');
    const backupData = JSON.parse(fileContent);
    
    // 数据结构验证
    if (!backupData.data || !backupData.version) {
      return { error: '备份文件格式不正确，缺少必要的数据结构' };
    }

    // 数据类型验证
    if (backupData.dataType !== 'other') {
      return { error: '请选择其他数据备份文件，账单数据请使用"导入账单"功能' };
    }

    return backupData;
  } catch (err) {
    throw err;
  }
};
```

### 3. 数据备份实现

```javascript
// src/screens/ProfileScreen.tsx
const handleDataBackup = useCallback(async () => {
  // 只获取其他数据，不包括账单数据
  const [creditCards, installmentPlans, loanRecords, products] = await Promise.all([
    databaseService.getAllCreditCards(),
    databaseService.getInstallmentPlans(),
    databaseService.getAllLoanRecords(),
    databaseService.getAllProducts()
  ]);

  // 创建其他数据的备份
  const backupData = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    dataType: 'other', // 明确标识数据类型
    data: {
      creditCards,
      installmentPlans,
      loanRecords,
      products: productsWithPrices
    }
  };

  // 生成明确的文件名
  const fileName = `雪球记账_其他数据备份_${timestamp}.json`;

  // 使用专门的备份文件分享函数
  await shareBackupFile(JSON.stringify(backupData, null, 2), fileName);
}, []);
```

### 4. 数据恢复实现

```javascript
const handleDataRestore = useCallback(async () => {
  try {
    // 使用专门的备份文件选择函数
    const result = await getBackupFileFromDocuments();
    
    if (result.error) {
      showToast(result.error, 'error');
      return;
    }

    // 成功获取备份数据，显示确认对话框
    setRestoreData(result);
    setRestoreConfirmVisible(true);
  } catch (error) {
    showToast('选择备份文件失败', 'error');
  }
}, []);
```

## 🛡️ 安全验证机制

### 文件验证层级

1. **文件扩展名检查**
   ```javascript
   if (!name.endsWith('.json')) {
     return { error: '请选择JSON格式的备份文件' };
   }
   ```

2. **文件名验证**
   ```javascript
   if (!name.includes('其他数据备份') && !name.includes('雪球记账')) {
     return { error: '请选择雪球记账的其他数据备份文件' };
   }
   ```

3. **JSON格式验证**
   ```javascript
   try {
     const backupData = JSON.parse(fileContent);
   } catch (parseError) {
     return { error: '备份文件格式错误或已损坏，无法解析JSON内容' };
   }
   ```

4. **数据结构验证**
   ```javascript
   if (!backupData.data || !backupData.version) {
     return { error: '备份文件格式不正确，缺少必要的数据结构' };
   }
   ```

5. **数据类型验证**
   ```javascript
   if (backupData.dataType !== 'other') {
     return { error: '请选择其他数据备份文件，账单数据请使用"导入账单"功能' };
   }
   ```

6. **数据内容验证**
   ```javascript
   if (!data.creditCards && !data.installmentPlans && 
       !data.loanRecords && !data.products) {
     return { error: '备份文件中没有找到支持的数据类型' };
   }
   ```

## 📁 文件格式规范

### 备份文件结构

```json
{
  "version": "1.0",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "dataType": "other",
  "data": {
    "creditCards": [
      {
        "id": "*************",
        "bankName": "招商银行",
        "lastThreeDigits": "123",
        "billingDay": 5,
        "paymentDueDay": 25,
        "color": "#FF6B6B",
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00"
      }
    ],
    "installmentPlans": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "type": "消费分期",
        "startDate": "2024-01-01",
        "totalAmount": 8999.00,
        "downPayment": 1000.00,
        "numberOfInstallments": 12,
        "installmentMode": "等额本息",
        "annualRate": 0.12,
        "handlingFee": 100.00,
        "autoRecord": 1
      }
    ],
    "loanRecords": [
      {
        "id": 1,
        "type": "lend",
        "amount": 5000.00,
        "note": "借给朋友",
        "loanDate": "2024-01-01",
        "repayments": [
          {
            "amount": 2500.00,
            "date": "2024-02-01"
          }
        ]
      }
    ],
    "products": [
      {
        "id": 1,
        "name": "MacBook Pro",
        "thumbnail": "/path/to/image.jpg",
        "notes": "工作用笔记本",
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00",
        "purchased": 0,
        "prices": [
          {
            "id": 1,
            "productId": 1,
            "price": 12999.00,
            "platform": "Apple官网",
            "isPurchasePrice": false,
            "createdAt": "2024-01-01 12:00:00",
            "updatedAt": "2024-01-01 12:00:00"
          }
        ]
      }
    ]
  }
}
```

### 文件命名规范

- **格式**：`雪球记账_其他数据备份_YYYY-MM-DD_HH-mm-ss.json`
- **示例**：`雪球记账_其他数据备份_2024-01-01_12-30-45.json`

## 🔄 用户体验流程

### 备份流程

1. **用户操作**：点击"数据备份"
2. **数据收集**：自动收集信用卡、分期账单、借款记录、愿望清单
3. **文件生成**：创建JSON格式备份文件
4. **文件分享**：调用系统分享功能

### 恢复流程

1. **用户操作**：点击"数据恢复"
2. **文件选择**：调用 `getBackupFileFromDocuments()`
3. **文件验证**：多层验证确保文件正确性
4. **确认对话框**：显示恢复内容和注意事项
5. **数据恢复**：逐项恢复数据到数据库

## ⚠️ 错误处理

### 常见错误及处理

| 错误类型 | 错误信息 | 处理方式 |
|----------|----------|----------|
| 文件格式错误 | "请选择JSON格式的备份文件" | 提示用户选择正确格式 |
| 文件来源错误 | "请选择雪球记账的其他数据备份文件" | 提示用户选择正确来源 |
| 数据类型错误 | "请选择其他数据备份文件，账单数据请使用'导入账单'功能" | 引导用户使用正确功能 |
| JSON解析错误 | "备份文件格式错误或已损坏，无法解析JSON内容" | 提示文件损坏 |
| 数据结构错误 | "备份文件格式不正确，缺少必要的数据结构" | 提示格式不正确 |
| 数据内容为空 | "备份文件中没有找到支持的数据类型" | 提示文件内容问题 |

## ✅ 功能优势

1. **职责分离**：账单数据和其他数据完全分离，避免混淆
2. **专门优化**：针对JSON格式的其他数据备份进行专门优化
3. **安全可靠**：多层验证确保数据安全和格式正确
4. **用户友好**：清晰的错误提示和操作指引
5. **向后兼容**：保持与现有账单导入/导出功能的兼容性

## 🎯 总结

通过创建专门的 `getBackupFileFromDocuments()` 函数，我们实现了：

- ✅ 功能职责清晰分离
- ✅ 专门的JSON文件处理
- ✅ 完善的验证机制
- ✅ 友好的错误处理
- ✅ 安全的数据恢复

现在用户可以安全、便捷地备份和恢复除账单数据外的所有其他重要数据！
