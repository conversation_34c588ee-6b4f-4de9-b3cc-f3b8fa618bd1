# 数据备份/恢复功能说明

## 功能概述

雪球记账现在支持完整的数据备份和恢复功能，不仅包括账单数据，还包括所有其他重要数据：信用卡、分期账单、借款记录、愿望清单等。

## 支持的数据类型

### 1. 账单数据
- 所有交易记录
- 分类信息
- 账本信息

### 2. 信用卡数据
- 信用卡基本信息
- 银行名称、卡号后三位
- 账单日、还款日
- 卡片颜色设置

### 3. 分期账单数据
- 分期计划信息
- 分期详情记录
- 还款状态

### 4. 借款记录
- 借入/借出记录
- 还款记录
- 借款状态

### 5. 愿望清单
- 商品信息
- 价格历史记录
- 购买状态

## 使用方法

### 数据备份

1. **进入个人中心**
2. **点击"数据备份"**
3. **等待备份完成**
4. **选择分享方式**（微信、邮件、保存到文件等）

#### 备份文件格式
- **文件名**：`雪球记账_完整备份_YYYY-MM-DD_HH-mm-ss.json`
- **文件格式**：JSON格式，包含完整的数据结构
- **文件大小**：根据数据量而定，通常几KB到几MB

#### 备份数据结构
```json
{
  "version": "1.0",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "transactions": [...],
    "categories": [...],
    "accountBooks": [...],
    "creditCards": [...],
    "installmentPlans": [...],
    "loanRecords": [...],
    "products": [...]
  }
}
```

### 数据恢复

1. **进入个人中心**
2. **点击"数据恢复"**
3. **选择备份文件**
4. **确认恢复操作**
5. **等待恢复完成**

#### 支持的文件格式
- **完整备份文件**：JSON格式的完整备份
- **账单数据文件**：CSV格式的账单数据（向后兼容）

#### 恢复策略
- **智能去重**：避免创建重复的分类和账本
- **数据合并**：新数据与现有数据合并，不会覆盖
- **错误容忍**：单项恢复失败不影响整体恢复

## 技术实现

### 备份流程

1. **数据收集**
   ```typescript
   const [
     transactions,
     categories,
     accountBooks,
     creditCards,
     installmentPlans,
     loanRecords,
     products
   ] = await Promise.all([
     databaseService.getAllTransactions(undefined),
     databaseService.getAllCategories(),
     databaseService.getAllAccountBooks(),
     databaseService.getAllCreditCards(),
     databaseService.getInstallmentPlans(),
     databaseService.getAllLoanRecords(),
     databaseService.getAllProducts()
   ]);
   ```

2. **数据处理**
   - 获取商品价格历史
   - 构建完整的备份数据结构
   - 添加版本信息和时间戳

3. **文件生成**
   - JSON序列化
   - 生成带时间戳的文件名
   - 调用分享功能

### 恢复流程

1. **文件选择**
   - 使用现有的文件选择功能
   - 支持JSON和CSV格式

2. **数据验证**
   - 检查文件格式
   - 验证数据结构

3. **分类恢复**
   ```typescript
   for (const category of data.categories) {
     await databaseService.addCategory({
       name: category.name,
       isExpense: category.isExpense,
       icon: category.icon || 'snowflake'
     });
   }
   ```

4. **数据恢复**
   - 按依赖关系顺序恢复
   - 错误隔离处理
   - 进度反馈

### 错误处理

#### 备份错误
- 数据库连接失败
- 数据读取错误
- 文件生成失败

#### 恢复错误
- 文件格式错误
- 数据结构不匹配
- 数据库写入失败

#### 容错机制
- 单项失败不影响整体
- 详细的错误日志
- 用户友好的错误提示

## 用户体验

### 进度反馈
- **备份过程**：`正在备份数据...`
- **恢复过程**：`正在恢复数据...`
- **完成提示**：`数据恢复成功，共恢复 X 项数据`

### 确认对话框
- **备份确认**：显示将要备份的数据类型
- **恢复确认**：警告可能的重复数据问题
- **操作结果**：显示成功恢复的数据数量

### 安全提示
- 备份文件包含敏感信息，请妥善保管
- 恢复操作不可撤销，请谨慎操作
- 建议定期备份重要数据

## 使用场景

### 1. 设备迁移
- 从旧设备导出完整备份
- 在新设备上恢复所有数据
- 保持完整的使用体验

### 2. 数据安全
- 定期备份防止数据丢失
- 重要操作前备份
- 多设备数据同步

### 3. 数据恢复
- 意外删除后恢复
- 应用重装后恢复
- 系统故障后恢复

### 4. 数据分析
- 导出数据进行分析
- 与其他工具集成
- 生成报表和统计

## 最佳实践

### 备份策略
1. **定期备份**：建议每周备份一次
2. **重要操作前备份**：清空数据、大量导入前
3. **多地备份**：云盘、邮箱、本地存储
4. **版本管理**：保留多个备份版本

### 恢复注意事项
1. **确认数据**：恢复前确认备份文件正确
2. **测试恢复**：在测试环境先试验
3. **分步恢复**：大量数据分批恢复
4. **验证结果**：恢复后检查数据完整性

### 文件管理
1. **命名规范**：使用时间戳命名
2. **分类存储**：按类型和时间分类
3. **定期清理**：删除过期备份
4. **安全存储**：加密敏感备份

## 兼容性

### 向后兼容
- 支持旧版本的账单CSV文件
- 自动转换为新的备份格式
- 保持现有导入功能

### 向前兼容
- 版本信息标识
- 可扩展的数据结构
- 优雅的降级处理

## 故障排除

### 常见问题

**Q: 备份文件过大怎么办？**
A: 可以分别备份不同类型的数据，或者清理历史数据后再备份。

**Q: 恢复时提示格式错误？**
A: 检查文件是否完整，是否为正确的JSON格式。

**Q: 恢复后数据重复？**
A: 这是正常现象，系统会尽量避免重复，但某些情况下可能产生重复数据。

**Q: 部分数据恢复失败？**
A: 查看错误日志，通常是数据格式不匹配或约束冲突导致。

### 技术支持
- 详细的错误日志
- 用户操作指南
- 常见问题解答
- 技术支持联系方式

## 总结

数据备份/恢复功能为雪球记账提供了完整的数据保护方案，不仅支持账单数据，还包括所有其他重要功能的数据。通过智能的备份策略和容错的恢复机制，确保用户数据的安全性和完整性。

### 主要优势
1. **全面覆盖**：支持所有数据类型
2. **操作简单**：一键备份和恢复
3. **格式通用**：JSON格式易于处理
4. **安全可靠**：多重错误处理机制
5. **向后兼容**：支持现有数据格式

这个功能让用户可以放心使用雪球记账，不用担心数据丢失或设备迁移的问题。
