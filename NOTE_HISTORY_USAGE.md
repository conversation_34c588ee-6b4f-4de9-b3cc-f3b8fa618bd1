# 备注历史功能使用说明

## 功能概述

现在雪球记账支持在导入数据时自动提取并保存备注历史，这样即使清除了所有交易数据，备注历史也能保留下来，让您的记账体验更加便捷。

## 使用方法

### 1. 导入包含备注的数据

当您导入包含备注信息的CSV文件时，系统会自动：
- 提取所有交易记录中的备注
- 按分类进行分组
- 去除重复的备注
- 保存到对应分类的备注历史中

### 2. 导入过程提示

导入过程中您会看到以下进度提示：
1. `正在准备导入数据...`
2. `正在导入数据... 50% (2500/5000)`
3. `正在保存备注历史...`
4. `已导入 5000 条记录，自动创建了 3 个账本，保存了 25 条备注历史`

### 3. 使用备注建议

导入完成后，当您添加新的交易记录时：
1. 选择分类
2. 点击备注输入框
3. 系统会显示该分类的历史备注建议
4. 点击任意建议即可快速输入

## 测试示例

### 测试数据
使用提供的 `test_note_history.csv` 文件进行测试，该文件包含：
- 13条交易记录
- 4个分类（餐饮、交通、购物、工资、兼职）
- 11个不同的备注

### 预期结果
导入后各分类的备注历史：

**餐饮分类**：
- 麦当劳早餐
- 午餐-沙县小吃
- 晚餐-火锅
- 咖啡
- 聚餐

**交通分类**：
- 地铁
- 公交车

**购物分类**：
- 超市购物
- 服装

**工资分类**：
- 月薪

**兼职分类**：
- 周末兼职

### 验证方法
1. 导入测试数据
2. 清除所有交易数据
3. 添加新交易，选择"餐饮"分类
4. 点击备注输入框
5. 应该能看到5个备注建议

## 功能特点

### 智能去重
- 相同分类下的重复备注只保存一次
- 例如："麦当劳早餐"出现2次，只保存1条历史记录

### 分类关联
- 每个分类维护独立的备注历史
- 备注建议只显示当前分类的相关历史

### 数据持久化
- 备注历史独立于交易数据存储
- 清除交易数据不会影响备注历史
- 重新导入数据会累积更多备注历史

### 批量处理
- 一次性处理所有导入数据中的备注
- 高效的数据库操作
- 详细的处理统计信息

## 注意事项

### 备注要求
- 备注不能为空
- 系统会自动去除前后空格
- 只有有效的备注才会被保存

### 分类关联
- 备注必须关联到有效的分类
- 如果分类不存在，备注不会被保存
- 建议先确保分类正确创建

### 性能考虑
- 大量数据导入时，备注提取可能需要一些时间
- 系统会显示进度提示，请耐心等待
- 备注历史的保存不会影响交易数据的导入

## 常见问题

### Q: 为什么我的备注历史没有保存？
A: 请检查：
- 导入的数据是否包含备注列
- 备注内容是否为空
- 分类是否正确匹配

### Q: 重复导入相同数据会产生重复备注吗？
A: 不会。系统会自动去重，相同分类下的相同备注只保存一次。

### Q: 可以手动管理备注历史吗？
A: 目前系统自动管理备注历史。每次添加交易时，如果输入了新备注，也会自动保存到历史中。

### Q: 备注历史有数量限制吗？
A: 每个分类最多显示10条最近的备注建议，但数据库中会保存所有历史备注。

## 最佳实践

### 1. 规范备注格式
建议使用统一的备注格式，例如：
- 餐饮：`早餐-麦当劳`、`午餐-沙县小吃`
- 交通：`地铁-2号线`、`公交-123路`
- 购物：`超市-日用品`、`网购-衣服`

### 2. 定期导出备份
定期导出数据可以：
- 备份您的备注历史
- 在多设备间同步备注
- 防止数据丢失

### 3. 合理使用分类
- 创建合适的分类层次
- 避免过于细分或过于宽泛
- 保持分类的一致性

## 技术说明

### 数据存储
备注历史存储在 `category_notes` 表中：
```sql
CREATE TABLE category_notes (
  id INTEGER PRIMARY KEY,
  category_id TEXT,
  note TEXT,
  created_at TEXT,
  UNIQUE(category_id, note)
);
```

### 处理流程
1. 扫描导入数据中的所有备注
2. 按分类ID分组
3. 使用Set去除重复备注
4. 批量保存到数据库
5. 更新备注历史索引

这个功能让您的记账体验更加智能和便捷，希望您会喜欢！
