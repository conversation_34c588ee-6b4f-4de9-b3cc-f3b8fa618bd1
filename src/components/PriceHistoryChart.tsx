import React from 'react';
import {View, StyleSheet, Text, Dimensions} from 'react-native';
import {
  VictoryLine,
  VictoryChart,
  VictoryAxis,
  VictoryScatter,
  VictoryLabel,
  VictoryGroup,
} from 'victory-native';
import dayjs from 'dayjs';
import {useTheme} from '../context/ThemeContext';

const {width} = Dimensions.get('window');

interface PriceData {
  id?: number;
  productId: number;
  price: number;
  platform?: string;
  isPurchasePrice?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PriceHistoryChartProps {
  prices: PriceData[];
}

// 预定义的颜色数组，用于区分不同平台
const PLATFORM_COLORS = [
  '#007AFF', // 蓝色
  '#FF3B30', // 红色
  '#34C759', // 绿色
  '#FF9500', // 橙色
  '#AF52DE', // 紫色
  '#FF2D92', // 粉色
  '#5AC8FA', // 浅蓝色
  '#FFCC00', // 黄色
];

const PriceHistoryChart: React.FC<PriceHistoryChartProps> = ({prices}) => {
  const {colors} = useTheme();

  if (!prices || prices.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>暂无价格记录</Text>
      </View>
    );
  }

  // 按平台分组数据
  const groupedByPlatform = prices.reduce((acc, price) => {
    const platform = price.platform || '未知平台';
    if (!acc[platform]) {
      acc[platform] = [];
    }
    acc[platform].push(price);
    return acc;
  }, {} as Record<string, PriceData[]>);

  // 获取所有平台名称
  const platforms = Object.keys(groupedByPlatform);

  // 如果只有一个平台或者没有平台信息，使用原来的简单显示方式
  if (platforms.length <= 1) {
    return renderSimpleChart(prices);
  }

  // 为每个平台排序数据并创建图表数据
  const platformChartData = platforms.map((platform, platformIndex) => {
    const platformPrices = groupedByPlatform[platform].sort((a, b) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });

    const chartData = platformPrices.map((price, index) => ({
      x: index + 1,
      y: Number(price.price) || 0,
      date: dayjs(price.createdAt).format('MM-DD'),
    }));

    return {
      platform,
      data: chartData,
      color: PLATFORM_COLORS[platformIndex % PLATFORM_COLORS.length],
    };
  });

  // 找出所有价格的最高价和最低价
  const allPrices = prices.map(p => p.price);
  const maxPrice = Math.max(...allPrices);
  const minPrice = Math.min(...allPrices);
  const priceRange = maxPrice - minPrice;

  // 计算适当的Y轴范围
  const yMin = Math.max(0, minPrice - (priceRange * 0.1 || 10));
  const yMax = maxPrice + (priceRange * 0.1 || 10);

  // 创建图例数据
  const legendData = platformChartData.map(p => ({
    name: p.platform,
    symbol: {fill: p.color, type: 'circle'},
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>价格历史走势</Text>

      {/* 图例 */}
      <View style={styles.legendContainer}>
        {legendData.map((legend, index) => (
          <View key={index} style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                {backgroundColor: legend.symbol.fill},
              ]}
            />
            <Text style={styles.legendText}>{legend.name}</Text>
          </View>
        ))}
      </View>

      <View style={styles.chartContainer}>
        <VictoryChart
          width={width - 40}
          height={220}
          padding={{top: 20, bottom: 40, left: 60, right: 20}}
          domain={{y: [yMin, yMax]}}>
          {/* Y轴 - 价格轴 */}
          <VictoryAxis
            dependentAxis
            tickFormat={t => `¥${t}`}
            style={{
              axis: {stroke: '#CCCCCC'},
              grid: {stroke: '#EEEEEE'},
              tickLabels: {fontSize: 12, padding: 5},
            }}
          />

          {/* X轴 - 序号轴 */}
          <VictoryAxis
            style={{
              axis: {stroke: '#CCCCCC'},
              tickLabels: {fontSize: 10, padding: 5},
            }}
            tickFormat={t => ''}
          />

          {/* 为每个平台绘制折线 */}
          {platformChartData.map((platformData, index) => {
            console.log('-----platformData', platformData);
            // 确保数据有效
            if (!platformData.data || platformData.data.length === 0) {
              return null;
            }
            return (
              <VictoryGroup key={index}>
                <VictoryLine
                  data={platformData.data}
                  style={{
                    data: {
                      stroke: platformData.color,
                      strokeWidth: 2,
                    },
                  }}
                  key={index}
                />
                <VictoryScatter
                  data={platformData.data}
                  size={4}
                  x="x"
                  y="y"
                  style={{
                    data: {
                      fill: platformData.color,
                    },
                  }}
                  labelComponent={
                    <VictoryLabel dy={-6} style={{fontSize: 9, fill: '#666'}} />
                  }
                  labels={({datum}) => `¥${datum.y}`}
                />
              </VictoryGroup>
            );
          })}
        </VictoryChart>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>历史最低</Text>
          <Text style={[styles.statValue, styles.lowestPrice]}>
            ¥{minPrice.toFixed(2)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>历史最高</Text>
          <Text style={[styles.statValue, styles.highestPrice]}>
            ¥{maxPrice.toFixed(2)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>价格波动</Text>
          <Text style={styles.statValue}>
            {minPrice > 0
              ? (((maxPrice - minPrice) / minPrice) * 100).toFixed(1)
              : '0.0'}
            %
          </Text>
        </View>
      </View>
    </View>
  );
};

// 简单图表渲染函数（单平台或无平台信息时使用）
const renderSimpleChart = (prices: PriceData[]) => {
  // 处理数据，按日期排序
  const sortedPrices = [...prices].sort((a, b) => {
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
  });

  // 格式化图表数据
  const chartData = sortedPrices.map((price, index) => ({
    x: index + 1,
    y: Number(price.price) || 0,
    date: dayjs(price.createdAt).format('MM-DD'),
  }));

  // 找出最高价和最低价
  const maxPrice = Math.max(...sortedPrices.map(p => p.price));
  const minPrice = Math.min(...sortedPrices.map(p => p.price));
  const priceRange = maxPrice - minPrice;

  // 计算适当的Y轴范围
  const yMin = Math.max(0, minPrice - (priceRange * 0.1 || 10));
  const yMax = maxPrice + (priceRange * 0.1 || 10);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>价格历史走势</Text>

      <View style={styles.chartContainer}>
        <VictoryChart
          width={width - 40}
          height={200}
          domainPadding={{x: 20, y: 10}}
          padding={{top: 20, bottom: 40, left: 60, right: 20}}
          domain={{y: [yMin, yMax]}}>
          {/* Y轴 - 价格轴 */}
          <VictoryAxis
            dependentAxis
            tickFormat={t => `¥${t}`}
            style={{
              axis: {stroke: '#CCCCCC'},
              grid: {stroke: '#EEEEEE'},
              tickLabels: {fontSize: 10, padding: 5},
            }}
          />

          {/* X轴 - 序号轴 */}
          <VictoryAxis
            style={{
              axis: {stroke: '#CCCCCC'},
              tickLabels: {fontSize: 10, padding: 5},
            }}
          />

          {/* 价格走势线 - 使用最基础的配置 */}
          <VictoryLine
            data={chartData}
            style={{
              data: {
                stroke: '#007AFF',
                strokeWidth: 2,
              },
            }}
          />

          {/* 价格点 */}
          <VictoryScatter
            data={chartData}
            size={5}
            style={{
              data: {
                fill: '#007AFF',
              },
            }}
            labelComponent={
              <VictoryLabel dy={-15} style={{fontSize: 10, fill: '#666'}} />
            }
            labels={({datum}) => `¥${datum.y}`}
          />
        </VictoryChart>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>历史最低</Text>
          <Text style={[styles.statValue, styles.lowestPrice]}>
            ¥{minPrice.toFixed(2)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>历史最高</Text>
          <Text style={[styles.statValue, styles.highestPrice]}>
            ¥{maxPrice.toFixed(2)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>价格波动</Text>
          <Text style={styles.statValue}>
            {minPrice > 0
              ? (((maxPrice - minPrice) / minPrice) * 100).toFixed(1)
              : '0.0'}
            %
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    // marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // marginBottom: 12,
    paddingHorizontal: 4,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  chartContainer: {
    alignItems: 'center',
    // marginBottom: 5,
  },
  emptyContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#999999',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: 12,
    // marginTop: 5,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  lowestPrice: {
    color: '#4CD964',
  },
  highestPrice: {
    color: '#FF3B30',
  },
});

export default PriceHistoryChart;
