import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {
  Transaction,
  Category,
} from '../services/DatabaseService';
import {
  VictoryPie,
  VictoryTheme,
  VictoryBar,
  VictoryChart,
  VictoryAxis,
  VictoryLabel,
  VictoryLine,
  VictoryScatter,
} from 'victory-native';
import {COLORS} from '../utils/color';
import {useFocusEffect} from '@react-navigation/native';
import {useTheme} from '../context/ThemeContext';

const {width, height} = Dimensions.get('window');

interface TransactionWithCategory extends Transaction {
  categoryName: string;
  categoryIcon: string;
}

// 在组件外部定义柔和的中等亮度色系调色板
const softColorPalette = [
  '#4CAF50', // 绿色
  '#2196F3', // 蓝色
  '#FF9800', // 橙色
  '#00BCD4', // 青色
  '#8BC34A', // 浅绿色
  '#03A9F4', // 浅蓝色
  '#FFC107', // 琥珀色
  '#009688', // 蓝绿色
  '#CDDC39', // 酸橙色
  '#607D8B', // 蓝灰色
];

const MonthDetailScreen = ({route, navigation}) => {
  const {year, month, familyId = ''} = route.params;
  const {colors} = useTheme();
  const [transactions, setTransactions] = useState<TransactionWithCategory[]>(
    [],
  );
  const [monthlyTotal, setMonthlyTotal] = useState({
    income: 0,
    expense: 0,
    balance: 0,
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [yearMonthPickerVisible, setYearMonthPickerVisible] = useState(false);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [selectedMonth, setSelectedMonth] = useState(month);
  const [selectedYear, setSelectedYear] = useState(year);
  const [dailyStats, setDailyStats] = useState<
    {date: string; amount: number}[]
  >([]);
  const [categoryStats, setCategoryStats] = useState<
    {name: string; amount: number; count: number; color: string}[]
  >([]);
  const [maxExpenseDay, setMaxExpenseDay] = useState({date: '', amount: 0});
  const [averageExpense, setAverageExpense] = useState(0);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [selectedDayIndex, setSelectedDayIndex] = useState(-1);
  const [pieChartExpanded, setPieChartExpanded] = useState(false);
  const [categoryRankExpanded, setCategoryRankExpanded] = useState(false);
  const [transactionsExpanded, setTransactionsExpanded] = useState(false);
  const [tempYear, setTempYear] = useState(year);
  const [viewType, setViewType] = useState<'expense' | 'income'>('expense');

  // 月份名称
  const monthNames = [
    '一月',
    '二月',
    '三月',
    '四月',
    '五月',
    '六月',
    '七月',
    '八月',
    '九月',
    '十月',
    '十一月',
    '十二月',
  ];

  // 加载数据
  const loadData = async () => {
    try {
      // 获取所有可用的年份
      const years = await databaseService.getTransactionYears(familyId);
      const currentYear = new Date().getFullYear();
      const recentYears = [];
      for (let i = 0; i < 10; i++) {
        recentYears.push(currentYear - i);
      }
      const allYears = [...new Set([...years, ...recentYears])].sort(
        (a, b) => b - a,
      );
      setAvailableYears(allYears);

      // 获取所有分类
      const allCategories = await databaseService.getAllCategories();
      setCategories(allCategories);

      // 获取月度统计数据
      const {income, expense} = await databaseService.getMonthlyStatistics(
        selectedYear,
        selectedMonth,
        familyId,
      );
      setMonthlyTotal({
        income,
        expense,
        balance: income - expense,
      });

      // 获取月度交易记录
      const monthTransactions = await databaseService.getTransactionsByMonth(
        selectedYear,
        selectedMonth,
        familyId,
      );

      // 根据 viewType 过滤交易记录
      const filteredTransactions = monthTransactions.filter(
        t => t.type === viewType,
      );

      // 添加分类信息
      const transactionsWithCategory = filteredTransactions.map(transaction => {
        const category = allCategories.find(
          c => c.id === transaction.categoryId,
        );
        return {
          ...transaction,
          categoryName: category ? category.name : '未知分类',
          categoryIcon: category ? category.icon : 'question',
        };
      });

      setTransactions(transactionsWithCategory);

      // 计算日统计数据
      const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
      const dailyStats = Array.from({length: daysInMonth}, (_, i) => {
        const day = i + 1;
        const date = `${selectedYear}-${selectedMonth
          .toString()
          .padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        return {date, amount: 0};
      });

      // 计算每日金额
      filteredTransactions.forEach(transaction => {
        const dateIndex = parseInt(transaction.date.split('-')[2]) - 1;
        if (dateIndex >= 0 && dateIndex < dailyStats.length) {
          dailyStats[dateIndex].amount += transaction.amount;
        }
      });

      setDailyStats(dailyStats);

      // 计算最高金额日
      const maxDay = dailyStats.reduce(
        (max, current) => (current.amount > max.amount ? current : max),
        {date: '', amount: 0},
      );
      setMaxExpenseDay({
        date: maxDay.date,
        amount: maxDay.amount,
      });

      // 计算日均金额
      const daysWithAmount = dailyStats.filter(day => day.amount > 0).length;
      setAverageExpense(
        daysWithAmount > 0
          ? dailyStats.reduce((sum, day) => sum + day.amount, 0) /
              daysWithAmount
          : 0,
      );

      // 计算类别统计
      const categoryMap = new Map();
      filteredTransactions.forEach(transaction => {
        const categoryId = transaction.categoryId;
        const category = allCategories.find(c => c.id === categoryId);
        const categoryName = category ? category.name : '未知分类';

        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, {
            name: categoryName,
            amount: 0,
            count: 0,
            icon: category ? category.icon : 'question',
            color: getRandomColor(categoryName),
          });
        }

        const categoryData = categoryMap.get(categoryId);
        categoryData.amount += transaction.amount;
        categoryData.count += 1;
      });

      setCategoryStats(Array.from(categoryMap.values()));
    } catch (error) {
      console.error('加载月度详情失败', error);
    }
  };

  // 使用 useFocusEffect 替代 useEffect 来加载数据
  useFocusEffect(
    React.useCallback(() => {
      console.log('MonthDetailScreen 获得焦点，重新加载数据');
      loadData();
      return () => {
        // 可选的清理函数
      };
    }, [selectedYear, selectedMonth, viewType]),
  );

  // 处理交易记录点击
  const handleTransactionPress = (transaction: Transaction) => {
    navigation.navigate('addTransaction', {transaction});
  };

  // 添加类别点击处理函数
  const handleCategoryPress = category => {
    navigation.navigate('categoryDetail', {
      categoryId: category.id,
      categoryName: category.name,
      categoryIcon: category.icon,
      categoryColor: category.color,
      year: selectedYear,
      month: selectedMonth,
      viewType,
      familyId,
    });
  };

  // 修改颜色生成函数
  const getRandomColor = (name: string) => {
    // 使用名称的哈希值来确定颜色，这样同名分类会有相同颜色
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    // 使用哈希值选择调色板中的颜色
    const index = Math.abs(hash) % softColorPalette.length;
    return softColorPalette[index];
  };

  // 渲染交易记录项
  const renderTransactionItem = ({item}: {item: TransactionWithCategory}) => {
    const isExpense = item.type === 'expense';
    const categoryColor = isExpense ? COLORS.functional.error : colors.primary;

    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => handleTransactionPress(item)}
        activeOpacity={0.7}>
        <View style={styles.transactionLeft}>
          <View
            style={[
              styles.categoryIcon,
              {backgroundColor: item.categoryColor || categoryColor},
            ]}>
            <Icon
              name={item.categoryIcon || 'question'}
              size={18}
              color="#FFFFFF"
            />
          </View>
          <View style={styles.transactionInfo}>
            <Text style={styles.categoryName}>{item.categoryName}</Text>
            {item.note ? (
              <Text style={styles.transactionNote} numberOfLines={1}>
                {item.note}
              </Text>
            ) : null}
            <Text style={styles.transactionDate}>
              {item.date.split('-')[2]}日
            </Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            {item.familyName ? (
              <View
                style={[
                  styles.familyNameBadge,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.familyName}>{item.familyName}</Text>
              </View>
            ) : null}
            {item.isReimbursable && (
              <View
                style={[
                  styles.reimbursableTag,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.reimbursableText}>报销账单</Text>
              </View>
            )}
          </View>
        </View>
        <Text
          style={[
            styles.transactionAmount,
            isExpense ? styles.expenseText : styles.incomeText,
          ]}>
          {isExpense ? '-￥' : '+￥'}
          {item.amount.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  };

  // 添加年月选择器渲染函数
  const renderYearMonthSelector = () => (
    <TouchableOpacity
      style={styles.yearMonthSelector}
      onPress={() => setYearMonthPickerVisible(true)}
      activeOpacity={0.7}>
      <Text style={styles.yearMonthSelectorText} numberOfLines={1}>
        {selectedYear}年{selectedMonth}月
      </Text>
      <Icon
        name="chevron-down"
        size={14}
        color="#666666"
        style={styles.yearMonthSelectorIcon}
      />
    </TouchableOpacity>
  );

  // 修改年月选择器弹窗
  const renderYearMonthPicker = () => {
    // 打开选择器时，初始化临时年份
    useEffect(() => {
      if (yearMonthPickerVisible) {
        setTempYear(selectedYear);
      }
    }, [yearMonthPickerVisible]);

    return (
      <Modal
        visible={yearMonthPickerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setYearMonthPickerVisible(false)}>
        <TouchableWithoutFeedback
          onPress={() => setYearMonthPickerVisible(false)}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择年月</Text>
                <TouchableOpacity
                  style={styles.closeButtonContainer}
                  onPress={() => setYearMonthPickerVisible(false)}>
                  <Text style={styles.closeButton}>关闭</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.yearMonthPickerContent}>
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerColumnTitle}>年份</Text>
                  <FlatList
                    data={availableYears}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={[
                          styles.pickerOption,
                          tempYear === item && styles.selectedPickerOption,
                        ]}
                        onPress={() => {
                          // 只更新临时年份状态
                          setTempYear(item);
                        }}>
                        <Text
                          style={[
                            styles.pickerOptionText,
                            tempYear === item &&
                              styles.selectedPickerOptionText,
                          ]}>
                          {item}年
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={styles.pickerList}
                    showsVerticalScrollIndicator={false}
                  />
                </View>

                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerColumnTitle}>月份</Text>
                  <FlatList
                    data={Array.from({length: 12}, (_, i) => i + 1)}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={[
                          styles.pickerOption,
                          selectedMonth === item && styles.selectedPickerOption,
                        ]}
                        onPress={() => {
                          // 选择月份时，一次性更新年份和月份，然后加载数据
                          setSelectedYear(tempYear);
                          setSelectedMonth(item);
                          setYearMonthPickerVisible(false);
                          // 使用新的年份和月份加载数据
                          loadData();
                        }}>
                        <Text
                          style={[
                            styles.pickerOptionText,
                            selectedMonth === item &&
                              styles.selectedPickerOptionText,
                          ]}>
                          {item}月
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={styles.pickerList}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  };

  // 修改日消费趋势图表，添加点击显示金额功能
  const renderDailyExpenseChart = () => {
    const fullFree = dailyStats.every(item => item.amount === 0);
    if (fullFree || !dailyStats || dailyStats.length === 0) {
      return (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>
            日{viewType === 'expense' ? '支出' : '收入'}趋势
          </Text>
          <View style={styles.emptyChartContainer}>
            <Text style={styles.emptyChartText}>暂无数据</Text>
          </View>
        </View>
      );
    }

    const validStats = dailyStats.filter(item => item && item.date);

    // 准备 Victory 折线图数据
    const chartData = validStats.map(item => {
      const day = parseInt(item.date.split('-')[2]);
      return {
        x: day,
        y: item.amount,
        date: item.date,
      };
    });

    // 找出最高金额日的索引
    const maxExpenseIndex = chartData.findIndex(
      item => item.date === maxExpenseDay.date,
    );

    // 计算最高日的交易笔数
    const maxDayTransactions = transactions.filter(
      t => t.type === viewType && t.date === maxExpenseDay.date,
    ).length;

    // 计算选中日的交易笔数
    const selectedDayTransactions =
      selectedDayIndex !== -1
        ? transactions.filter(
            t =>
              t.type === viewType &&
              t.date === validStats[selectedDayIndex].date,
          ).length
        : 0;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          日{viewType === 'expense' ? '支出' : '收入'}趋势
        </Text>

        {/* 显示最高金额日信息 */}
        {maxExpenseDay && maxExpenseDay.date && (
          <View style={styles.maxExpenseDayInfoCompact}>
            <View style={styles.maxExpenseDayLeftContent}>
              <Text style={styles.maxExpenseDayTitle}>
                {viewType === 'expense' ? '支出' : '收入'}最高日
              </Text>
              <Text style={styles.maxExpenseDayDate}>
                {maxExpenseDay.date.split('-')[2]}日
              </Text>
            </View>
            <View style={styles.maxExpenseDayRightContent}>
              <Text style={styles.maxExpenseDayAmount}>
                ￥{maxExpenseDay.amount.toFixed(2)}
              </Text>
              <Text style={styles.maxExpenseDayCount}>
                {maxDayTransactions}笔交易
              </Text>
            </View>
          </View>
        )}

        {/* 显示选中日信息（如果不是最高日） */}
        {selectedDayIndex !== -1 &&
          selectedDayIndex !== maxExpenseIndex &&
          validStats[selectedDayIndex] && (
            <View style={styles.selectedDayInfoCompact}>
              <View style={styles.selectedDayLeftContent}>
                <Text style={styles.selectedDayTitle}>已选择</Text>
                <Text style={styles.selectedDayDate}>
                  {validStats[selectedDayIndex].date.split('-')[2]}日
                </Text>
              </View>
              <View style={styles.selectedDayRightContent}>
                <Text style={styles.selectedDayAmount}>
                  ￥{validStats[selectedDayIndex].amount.toFixed(2)}
                </Text>
                <Text style={styles.selectedDayCount}>
                  {selectedDayTransactions}笔交易
                </Text>
              </View>
            </View>
          )}

        <View style={styles.victoryLineContainer}>
          <VictoryChart
            width={width - 40}
            height={220}
            domainPadding={{x: 10, y: 10}}
            padding={{top: 30, bottom: 40, left: 50, right: 20}}>
            <VictoryAxis
              tickFormat={t => `${t}日`}
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {
                  fontSize: 10,
                  fill: '#333333',
                },
              }}
            />
            <VictoryAxis
              dependentAxis
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {fontSize: 10, fill: '#333333'},
                grid: {stroke: '#EEEEEE'},
              }}
              tickFormat={t => `￥${t}`}
            />
            <VictoryLine
              interpolation="natural"
              data={chartData}
              x="x"
              y="y"
              style={{
                data: {
                  stroke: '#2196F3',
                  strokeWidth: 2,
                },
              }}
            />
            <VictoryScatter
              data={chartData}
              x="x"
              y="y"
              size={props => {
                // 检查是否是选中的点或最高点
                const isSelected = props.index === selectedDayIndex;
                const isMaxPoint = props.datum.date === maxExpenseDay.date;
                return isSelected || isMaxPoint ? 6 : 4;
              }}
              style={{
                data: {
                  fill: props => {
                    // 根据点的状态设置不同的颜色
                    if (props.datum.date === maxExpenseDay.date) {
                      return '#FF3B30'; // 最高点使用红色
                    } else if (props.index === selectedDayIndex) {
                      return '#007AFF'; // 选中点使用蓝色
                    } else {
                      return '#2196F3'; // 普通点使用浅蓝色
                    }
                  },
                  stroke: '#FFFFFF',
                  strokeWidth: 2,
                },
              }}
              labels={props => {
                // 只有当点被选中时才显示标签
                if (props.index === selectedDayIndex) {
                  return `￥${props.datum.y.toFixed(2)}`;
                }
                return null;
              }}
              labelComponent={
                <VictoryLabel
                  dy={-15}
                  style={{
                    fontSize: 12,
                    fill: '#333333',
                    fontWeight: 'bold',
                  }}
                  backgroundStyle={{
                    fill: 'white',
                    stroke: '#CCCCCC',
                    strokeWidth: 1,
                    rx: 3,
                  }}
                  backgroundPadding={{top: 5, bottom: 3, left: 10, right: 10}}
                />
              }
              events={[
                {
                  target: 'data',
                  eventHandlers: {
                    // 使用多种事件类型来确保在不同设备上都能响应
                    onPress: (_, props) => {
                      setSelectedDayIndex(props.index);
                      return null;
                    },
                    onPressIn: (_, props) => {
                      setSelectedDayIndex(props.index);
                      return null;
                    },
                    onTouchStart: (_, props) => {
                      setSelectedDayIndex(props.index);
                      return null;
                    },
                  },
                },
              ]}
            />
          </VictoryChart>
        </View>
      </View>
    );
  };

  // 修改支出类型占比饼图，保持图表完整，只限制列表显示
  const renderCategoryPieChart = () => {
    if (!categoryStats || categoryStats.length === 0) {
      return (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>支出类型占比</Text>
          <View style={styles.emptyChartContainer}>
            <Text style={styles.emptyChartText}>暂无数据</Text>
          </View>
        </View>
      );
    }

    const totalAmount = categoryStats.reduce(
      (sum, item) => sum + item.amount,
      0,
    );

    // 按金额排序并分配颜色
    const sortedStats = [...categoryStats].sort((a, b) => b.amount - a.amount);

    // 为每个类别找到对应的图标
    const statsWithIcons = sortedStats.map(item => {
      const category = categories.find(c => c.name === item.name);
      return {
        ...item,
        icon: category ? category.icon : 'question',
        percentage: ((item.amount / totalAmount) * 100).toFixed(1) + '%',
      };
    });

    const pieData = statsWithIcons.map(item => ({
      name: item.name || '未知',
      population: item.amount,
      color: getRandomColor(item.name),
      legendFontColor: '#333333',
      legendFontSize: 12,
      percentage: item.percentage,
    }));

    // 决定显示多少个类别（只影响列表，不影响图表）
    const displayCount = pieChartExpanded
      ? statsWithIcons.length
      : Math.min(5, statsWithIcons.length);
    const displayStats = statsWithIcons.slice(0, displayCount);

    // 是否需要显示展开/收起按钮
    const showToggleButton = statsWithIcons.length > 5;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          {viewType === 'expense' ? '支出' : '收入'}类型占比
        </Text>

        {/* 饼图显示全部数据 */}
        <VictoryPie
          width={width - 40}
          height={200}
          data={pieData}
          theme={VictoryTheme.clean}
          x="name"
          y="population"
          innerRadius={80}
          padAngle={0}
          labelPlacement="perpendicular"
          labels={({datum}) => {
            // 计算占比
            const total = pieData.reduce(
              (sum, item) => sum + item.population,
              0,
            );
            const percentage = (datum.population / total) * 100;

            // 如果占比小于 10%，则不显示标签
            if (percentage < 10) {
              return null;
            }

            // 如果名称超过2个字，则用省略号
            const name = datum.name;
            return name.length > 2 ? name.substring(0, 2) + '...' : name;
          }}
          style={{
            data: {
              fill: ({datum}) => datum.color,
              stroke: '#FFFFFF',
              strokeWidth: 0,
            },
            labels: {
              fill: '#FFFFFF',
              fontSize: 12,
              fontWeight: 'bold',
            },
          }}
        />

        {/* 显示百分比 - 使用图标代替颜色点，但限制显示数量 */}
        <View style={styles.categoryDetailList}>
          {displayStats.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.categoryDetailItem}
              onPress={() => {
                const category = categories.find(c => c.name === item.name);
                if (category) {
                  handleCategoryPress({
                    id: category.id,
                    name: item.name,
                    icon: item.icon,
                    color: item.color,
                  });
                }
              }}>
              <View style={styles.categoryDetailHeader}>
                <View
                  style={[
                    styles.categoryIconSmall,
                    {backgroundColor: item.color},
                  ]}>
                  <Icon name={item.icon} size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.categoryDetailName}>{item.name}</Text>
              </View>
              <View style={styles.categoryDetailInfo}>
                <Text style={styles.categoryDetailAmount}>
                  {item.percentage}
                </Text>
                <Text style={styles.categoryDetailCount}>
                  ￥{item.amount.toFixed(2)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}

          {/* 展开/收起按钮 */}
          {showToggleButton && (
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setPieChartExpanded(!pieChartExpanded)}>
              <Text style={[styles.toggleButtonText, {color: colors.primary}]}>
                {pieChartExpanded ? '收起' : '展开'}
              </Text>
              <Icon
                name={pieChartExpanded ? 'chevron-up' : 'chevron-down'}
                size={14}
                color={colors.primary}
                style={styles.toggleButtonIcon}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  // 添加一个计算柱状图高度的函数
  const calculateChartHeight = (dataLength: number) => {
    // 设置最小高度和每个数据项的高度
    const minHeight = 150; // 最小高度
    const heightPerItem = 46; // 每个数据项的高度

    // 根据数据量计算高度，但不低于最小高度
    return Math.max(minHeight, dataLength * heightPerItem);
  };

  // 修改支出类型排行的函数，只限制列表显示，不影响图表
  const renderCategoryRanking = () => {
    if (!categoryStats || categoryStats.length === 0) {
      return null;
    }

    // 按金额排序
    const topCategories = [...categoryStats].sort(
      (a, b) => b.amount - a.amount,
    );

    // 准备 Victory 柱状图数据
    const chartData = topCategories.map(item => {
      // 如果名称超过4个字，则用省略号
      const displayName =
        item.name.length > 4 ? item.name.substring(0, 4) + '...' : item.name;

      return {
        x: displayName,
        y: item.amount,
        color: item.color,
        label: `￥${item.amount.toFixed(0)}`,
        fullName: item.name, // 保存完整名称用于图例显示
      };
    });

    const chartHeight = calculateChartHeight(topCategories.length);

    // 决定显示多少个类别（只影响列表，不影响图表）
    const displayCount = categoryRankExpanded
      ? topCategories.length
      : Math.min(5, topCategories.length);
    const displayCategories = topCategories.slice(0, displayCount);

    // 是否需要显示展开/收起按钮
    const showToggleButton = topCategories.length > 5;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          {viewType === 'expense' ? '支出' : '收入'}类型排行
        </Text>

        <View style={styles.victoryBarContainer}>
          <VictoryChart
            width={width - 40}
            height={chartHeight}
            domainPadding={{x: 25}}
            padding={{top: 0, bottom: 50, left: 40, right: 60}}
            standalone={true}>
            <VictoryAxis
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {
                  fontSize: 10,
                  padding: 5,
                  angle: -45,
                  textAnchor: 'end',
                  fill: '#333333',
                },
              }}
            />
            <VictoryAxis
              dependentAxis
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {fontSize: 10, fill: '#333333'},
                grid: {stroke: '#EEEEEE'},
              }}
              tickFormat={t => `￥${t}`}
            />
            <VictoryBar
              horizontal
              data={chartData.reverse()}
              x="x"
              y="y"
              cornerRadius={{top: 5}}
              barWidth={20}
              style={{
                data: {
                  fill: ({datum}) => datum.color,
                  stroke: '#FFFFFF',
                  strokeWidth: 1,
                },
              }}
              labels={({datum}) => datum.label}
              labelComponent={
                <VictoryLabel style={{fontSize: 10, fill: '#333333'}} />
              }
            />
          </VictoryChart>
        </View>

        {/* 类别图例 - 限制显示数量 */}
        <View style={styles.categoryLegendContainer}>
          {displayCategories.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.categoryLegendItem}
              onPress={() => {
                const category = categories.find(c => c.name === item.name);
                if (category) {
                  handleCategoryPress({
                    id: category.id,
                    name: item.name,
                    icon: item.icon || 'question',
                    color: item.color,
                  });
                }
              }}>
              <View style={styles.categoryDetailHeader}>
                <View
                  style={[
                    styles.categoryIconSmall,
                    {backgroundColor: item.color},
                  ]}>
                  <Icon
                    name={item.icon || 'question'}
                    size={20}
                    color="#FFFFFF"
                  />
                </View>
                <Text style={styles.categoryDetailName}>{item.name}</Text>
              </View>
              <View style={styles.categoryDetailInfo}>
                <Text style={styles.categoryDetailAmount}>
                  ￥{item.amount.toFixed(2)}
                </Text>
                <Text style={styles.categoryDetailCount}>
                  {item.count}笔交易
                </Text>
              </View>
            </TouchableOpacity>
          ))}

          {/* 展开/收起按钮 */}
          {showToggleButton && (
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setCategoryRankExpanded(!categoryRankExpanded)}>
              <Text style={[styles.toggleButtonText, {color: colors.primary}]}>
                {categoryRankExpanded ? '收起' : '展开'}
              </Text>
              <Icon
                name={categoryRankExpanded ? 'chevron-up' : 'chevron-down'}
                size={14}
                color={colors.primary}
                style={styles.toggleButtonIcon}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  // 修改交易记录部分
  const renderTransactions = () => {
    if (transactions.length === 0) {
      return (
        <View style={styles.transactionsContainer}>
          <Text style={styles.sectionTitle}>交易记录</Text>
          <View style={styles.emptyContainer}>
            <Icon name="receipt" size={50} color="#CCCCCC" />
            <Text style={styles.emptyText}>暂无交易记录</Text>
          </View>
        </View>
      );
    }

    // 决定显示多少个交易
    const displayCount = transactionsExpanded
      ? transactions.length
      : Math.min(5, transactions.length);
    const displayTransactions = transactions.slice(0, displayCount);

    // 是否需要显示展开/收起按钮
    const showToggleButton = transactions.length > 5;

    return (
      <View style={styles.transactionsContainer}>
        <Text style={styles.sectionTitle}>交易记录</Text>
        <FlatList
          data={displayTransactions}
          renderItem={renderTransactionItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.transactionsList}
          scrollEnabled={false}
        />

        {/* 展开/收起按钮 */}
        {showToggleButton && (
          <TouchableOpacity
            style={styles.toggleButton}
            onPress={() => setTransactionsExpanded(!transactionsExpanded)}>
            <Text style={[styles.toggleButtonText, {color: colors.primary}]}>
              {transactionsExpanded ? '收起' : '展开'}
            </Text>
            <Icon
              name={transactionsExpanded ? 'chevron-up' : 'chevron-down'}
              size={14}
              color={colors.primary}
              style={styles.toggleButtonIcon}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 修改浮动按钮的点击事件处理
  const renderFloatingButton = () => (
    <TouchableOpacity
      style={[
        styles.floatingButton,
        {
          backgroundColor:
            viewType === 'expense' ? COLORS.expense : COLORS.income,
          bottom: height / 2,
        },
      ]}
      onPress={() => {
        setViewType(prev => (prev === 'expense' ? 'income' : 'expense'));
      }}>
      <View style={styles.floatingButtonContent}>
        <Icon
          name={viewType === 'expense' ? 'arrow-down' : 'arrow-up'}
          size={20}
          color="#FFFFFF"
        />
      </View>
    </TouchableOpacity>
  );

  return (
    <PageContainer
      headerTitle={`${monthNames[selectedMonth - 1]}账单`}
      backgroundColor={COLORS.secondary}
      rightComponent={renderYearMonthSelector()}>
      <ScrollView style={styles.container}>
        {/* 月度统计卡片 */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>月度统计</Text>

          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>月度收入</Text>
              <Text style={[styles.summaryValue, styles.incomeText]}>
                ￥{monthlyTotal.income.toFixed(2)}
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>月度支出</Text>
              <Text style={[styles.summaryValue, styles.expenseText]}>
                ￥{monthlyTotal.expense.toFixed(2)}
              </Text>
            </View>
          </View>

          <View style={styles.balanceContainer}>
            <Text style={styles.balanceLabel}>月度结余</Text>
            <Text
              style={[
                styles.balanceValue,
                monthlyTotal.balance >= 0
                  ? styles.incomeText
                  : styles.expenseText,
              ]}>
              ￥{monthlyTotal.balance.toFixed(2)}
            </Text>
          </View>
        </View>

        {/* 日消费图表 */}
        {renderDailyExpenseChart()}

        {/* 支出类型占比饼图 */}
        {renderCategoryPieChart()}

        {/* 支出类型排行条形图 */}
        {renderCategoryRanking()}

        {/* 交易记录列表 */}
        {renderTransactions()}
      </ScrollView>

      {/* 年月选择器 */}
      {renderYearMonthPicker()}

      {/* 浮动切换按钮 */}
      {renderFloatingButton()}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  summaryCard: {
    margin: 15,
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryItem: {
    flex: 1,
    backgroundColor: COLORS.secondary,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 5,
  },
  summaryLabel: {
    fontSize: 14,
    color: COLORS.text.primary,
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
  },
  balanceContainer: {
    backgroundColor: COLORS.secondary,
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 5,
  },
  balanceLabel: {
    fontSize: 16,
    color: COLORS.text.primary,
    marginBottom: 5,
    textAlign: 'center',
  },
  balanceValue: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
  },
  incomeText: {
    color: '#34C759',
  },
  expenseText: {
    color: '#FF3B30',
  },
  transactionsContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 15,
    marginLeft: 5,
  },
  transactionsList: {
    // paddingBottom: 20,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    marginBottom: 4,
    padding: 10,
    shadowColor: 'rgba(0, 0, 0, 0.08)',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  transactionInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 2,
  },
  transactionNote: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: '#999999',
  },
  familyNameBadge: {
    marginRight: 20,
    backgroundColor: '#FFF9C4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  familyName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  reimbursableTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 10, // 与金额的间距
  },
  reimbursableText: {
    fontSize: 12,
    color: '#FFFFFF', // 待报销标签的文字颜色
    fontWeight: '600',
  },
  transactionAmount: {
    width: 100,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.text.primary,
    marginTop: 15,
  },
  yearMonthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
    maxWidth: 120,
  },
  yearMonthSelectorText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginRight: 5,
    flex: 1,
  },
  yearMonthSelectorIcon: {
    marginLeft: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: COLORS.secondary,
    // borderBottomWidth: 1,
    // borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
  },
  closeButtonContainer: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
  },
  closeButton: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
  },
  yearMonthPickerContent: {
    flexDirection: 'row',
    padding: 15,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerColumnTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 10,
  },
  pickerList: {
    maxHeight: 250,
  },
  pickerOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    alignItems: 'center',
  },
  selectedPickerOption: {
    backgroundColor: COLORS.secondary,
  },
  pickerOptionText: {
    fontSize: 16,
    color: COLORS.text.primary,
    textAlign: 'center',
  },
  selectedPickerOptionText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  chartContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    margin: 15,
    padding: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 15,
    textAlign: 'center',
  },
  emptyChartContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyChartText: {
    fontSize: 16,
    color: '#999999',
  },
  analysisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  analysisItem: {
    alignItems: 'center',
  },
  analysisLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  analysisValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  categoryDetailList: {
    width: '100%',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  categoryDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIconSmall: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  categoryDetailName: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  categoryDetailInfo: {
    alignItems: 'flex-end',
  },
  categoryDetailAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  categoryDetailCount: {
    fontSize: 12,
    color: '#666666',
  },
  selectedDayInfo: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  selectedDayDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  selectedDayAmount: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 4,
  },
  chartTouchable: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  maxExpenseDayInfoCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    width: '100%',
  },
  maxExpenseDayLeftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  maxExpenseDayRightContent: {
    alignItems: 'flex-end',
  },
  maxExpenseDayTitle: {
    fontSize: 14,
    color: '#666666',
    marginRight: 8,
  },
  maxExpenseDayDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  maxExpenseDayAmount: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FF3B30',
  },
  maxExpenseDayCount: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  selectedDayInfoCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    width: '100%',
  },
  selectedDayLeftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedDayRightContent: {
    alignItems: 'flex-end',
  },
  selectedDayTitle: {
    fontSize: 14,
    color: '#666666',
    marginRight: 8,
  },
  selectedDayDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  selectedDayAmount: {
    fontSize: 16,
    fontWeight: '700',
    color: '#007AFF',
  },
  selectedDayCount: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  victoryBarContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  categoryLegendContainer: {
    width: '100%',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  categoryLegendItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  victoryLineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // marginVertical: 10,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginTop: 5,
    // borderTopWidth: 1,
    // borderTopColor: '#F0F0F0',
  },
  toggleButtonText: {
    fontSize: 16,
    color: COLORS.primary,
    marginRight: 5,
  },
  toggleButtonIcon: {
    marginTop: 1,
  },
  floatingButton: {
    position: 'absolute',
    right: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
  },
  floatingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  floatingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default MonthDetailScreen;
