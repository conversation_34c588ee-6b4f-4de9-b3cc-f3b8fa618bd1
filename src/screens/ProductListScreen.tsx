import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useFocusEffect} from '@react-navigation/native';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import EmptyState from '../components/EmptyState';
import {useTheme} from '../context/ThemeContext';

type TabType = 'all' | 'unpurchased' | 'purchased';

// 自定义圆形复选框组件
const CircleCheckbox = ({checked, onChange, style}) => {
  return (
    <TouchableOpacity
      style={[
        styles.circleCheckbox,
        checked && styles.circleCheckboxChecked,
        style,
      ]}
      onPress={onChange}>
      {checked && <Icon name="check" size={12} color="#FFFFFF" />}
    </TouchableOpacity>
  );
};

// 平台标签映射
const getPlatformLabel = (value) => {
  // 确保返回值始终是字符串
  if (!value || typeof value !== 'string') {
    return '未知';
  }

  const platformMap = {
    'jd': '京东',
    'taobao': '淘宝',
    'tmall': '天猫',
    'pdd': '拼多多',
    'suning': '苏宁',
    'vip': '唯品会',
    'amazon': '亚马逊',
    'offline': '实体店',
  };

  return platformMap[value] || value || '未知';
};

const ProductListScreen = ({navigation}) => {
  const {colors} = useTheme();
  const [products, setProducts] = useState<any[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState<TabType>('all');
  const [selectedProducts, setSelectedProducts] = useState<{
    [key: number]: boolean;
  }>({});
  const [productQuantities, setProductQuantities] = useState<{
    [key: number]: number;
  }>({});
  const [viewMode, setViewMode] = useState<'single' | 'double'>('single');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isAllSelected, setIsAllSelected] = useState(false);

  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      const productsData = await databaseService.getAllProducts();

      // 加载每个商品的价格记录（包括购买价格标记）
      const productsWithPrices = await Promise.all(
        productsData.map(async product => {
          if (product.id) {
            const prices = await databaseService.getProductPrices(product.id);
            // 确保价格记录包含isPurchasePrice字段，将is_purchase_price转为isPurchasePrice
            const formattedPrices = prices.map(price => ({
              ...price,
              isPurchasePrice: price.isPurchasePrice === true,
            }));

            return {
              ...product,
              prices: formattedPrices || [],
            };
          }
          return {...product, prices: []};
        }),
      );

      setProducts(productsWithPrices);
      setSelectedProducts({}); // 重置选择
    } catch (error) {
      console.error('加载商品列表失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadProducts();
    }, [loadProducts])
  );

  // 筛选与搜索
  useEffect(() => {
    let filtered = [...products];

    // 根据Tab进行筛选
    if (activeTab === 'purchased') {
      filtered = filtered.filter(product => product.purchased === 1);
    } else if (activeTab === 'unpurchased') {
      filtered = filtered.filter(product => product.purchased === 0);
    }

    // 根据搜索文本进行筛选
    if (searchText.trim() !== '') {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchText.toLowerCase()),
      );
    }
    // 清除已选中的商品
    setSelectedProducts({});

    setFilteredProducts(filtered);
  }, [searchText, products, activeTab]);

  const handleAddProduct = () => {
    navigation.navigate('productEdit', {});
  };

  const handleProductPress = productId => {
    // 如果有任何商品被选中，点击商品不进入详情页而是切换选中状态
    if (hasSelectedProducts) {
      toggleProductSelection(productId);
    } else {
      navigation.navigate('productDetail', {productId});
    }
  };

  const toggleProductSelection = productId => {
    setSelectedProducts(prev => {
      const newState = {
        ...prev,
        [productId]: !prev[productId],
      };

      // 如果是新选中的商品，初始化数量为1
      if (newState[productId] && !productQuantities[productId]) {
        setProductQuantities(prev => ({
          ...prev,
          [productId]: 1,
        }));
      }

      // 检查是否全部选中
      const allSelected = filteredProducts.every(product =>
        newState[product.id]
      );
      setIsAllSelected(allSelected);

      return newState;
    });
  };

  // 增加商品数量
  const increaseQuantity = (productId) => {
    setProductQuantities(prev => ({
      ...prev,
      [productId]: (prev[productId] || 1) + 1,
    }));
  };

  // 减少商品数量
  const decreaseQuantity = (productId) => {
    setProductQuantities(prev => ({
      ...prev,
      [productId]: Math.max(1, (prev[productId] || 1) - 1),
    }));
  };

  // 清空选择时也清空数量
  const clearAllSelections = () => {
    setSelectedProducts({});
    setProductQuantities({});
    setIsAllSelected(false);
  };

  // 获取商品的最新价格
  const getLatestPrice = product => {
    if (product.prices && product.prices.length > 0) {
      // 根据创建时间排序，返回最新的价格
      const sortedPrices = [...product.prices].sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
      return {price: sortedPrices[0].price, platform: sortedPrices[0].platform};
    }
    return null;
  };

  // 获取商品的购买价格 - 修改为使用标记
  const getPurchasePrice = product => {
    if (product.purchased && product.prices && product.prices.length > 0) {
      // 查找标记为购买价格的记录
      const purchasePriceRecord = product.prices.find(
        price => price.isPurchasePrice === true,
      );
      if (purchasePriceRecord) {
        return {
          price: purchasePriceRecord.price,
          platform: purchasePriceRecord.platform,
        };
      }
    }
    return null;
  };

  // 修改计算总价的逻辑，考虑商品数量
  const priceCalculation = useMemo(() => {
    let totalWithPurchasePrice = 0;
    let totalWithLatestPrice = 0;
    let hasSelectedPurchasedProduct = false;

    Object.keys(selectedProducts).forEach(productId => {
      if (selectedProducts[Number(productId)]) {
        const product = products.find(p => p.id === Number(productId));
        const quantity = productQuantities[Number(productId)] || 1; // 获取商品数量，默认为1

        if (product) {
          const latestPrice = getLatestPrice(product)?.price;

          // 计算所有商品使用最新价格的总和
          if (latestPrice !== null) {
            totalWithLatestPrice += latestPrice * quantity; // 乘以数量
          }

          // 对于已购买商品，使用购买价格；未购买商品，使用最新价格
          if (product.purchased === 1) {
            hasSelectedPurchasedProduct = true;
            const purchasePrice = getPurchasePrice(product)?.price;
            if (purchasePrice !== null) {
              totalWithPurchasePrice += purchasePrice * quantity; // 乘以数量
            } else if (latestPrice !== null) {
              totalWithPurchasePrice += latestPrice * quantity; // 乘以数量
            }
          } else if (latestPrice !== null) {
            totalWithPurchasePrice += latestPrice * quantity; // 乘以数量
          }
        }
      }
    });

    // 计算差值和更划算的方式
    const difference = Math.abs(totalWithLatestPrice - totalWithPurchasePrice);
    const isBetterWithPurchasePrice = totalWithPurchasePrice < totalWithLatestPrice;

    return {
      totalWithPurchasePrice: totalWithPurchasePrice.toFixed(2),
      totalWithLatestPrice: totalWithLatestPrice.toFixed(2),
      hasSelectedPurchasedProduct,
      difference: difference.toFixed(2),
      isBetterWithPurchasePrice,
    };
  }, [selectedProducts, products, productQuantities]); // 添加 productQuantities 作为依赖项

  // 是否有商品被选中
  const hasSelectedProducts = useMemo(() => {
    return Object.values(selectedProducts).some(selected => selected);
  }, [selectedProducts]);

  // 选中的商品数量
  const selectedCount = useMemo(() => {
    return Object.values(selectedProducts).filter(selected => selected).length;
  }, [selectedProducts]);

  // 切换视图模式
  const toggleViewMode = () => {
    setViewMode(prev => (prev === 'single' ? 'double' : 'single'));
  };

  // 批量删除选中的商品
  const handleBatchDelete = async () => {
    // 关闭确认对话框
    setShowDeleteConfirm(false);

    // 获取所有选中的商品ID
    const selectedIds = Object.keys(selectedProducts)
      .filter(id => selectedProducts[Number(id)])
      .map(id => Number(id));

    if (selectedIds.length === 0) {return;}

    // 显示加载状态
    setLoading(true);

    try {
      // 依次删除所有选中的商品
      for (const productId of selectedIds) {
        await databaseService.deleteProduct(productId);
      }

      // 清空选择
      setSelectedProducts({});

      // 重新加载商品列表
      await loadProducts();
    } catch (error) {
      console.error('批量删除失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 全选/取消全选功能
  const toggleSelectAll = () => {
    if (isAllSelected) {
      // 如果当前是全选状态，取消所有选择
      setSelectedProducts({});
      setProductQuantities({});
      setIsAllSelected(false);
    } else {
      // 选择所有过滤后的商品
      const newSelectedProducts = {};
      const newProductQuantities = {};

      filteredProducts.forEach(product => {
        newSelectedProducts[product.id] = true;
        newProductQuantities[product.id] = productQuantities[product.id] || 1;
      });

      setSelectedProducts(newSelectedProducts);
      setProductQuantities(newProductQuantities);
      setIsAllSelected(true);
    }
  };

  // 每次过滤商品后更新全选状态
  useEffect(() => {
    // 如果所有过滤后的商品都被选中，则全选状态为true
    const allSelected = filteredProducts.length > 0 &&
      filteredProducts.every(product => selectedProducts[product.id]);
    setIsAllSelected(allSelected);
  }, [filteredProducts, selectedProducts]);

  const renderTabBar = () => {
    if (products.length < 1) {
      return null;
    }
    return (
      <View style={styles.tabBarContainer}>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'all' && styles.activeTabItem]}
          onPress={() => setActiveTab('all')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'all' && styles.activeTabText,
            ]}>
            全部
          </Text>
          {activeTab === 'all' && <View style={styles.tabIndicator} />}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabItem,
            activeTab === 'unpurchased' && styles.activeTabItem,
          ]}
          onPress={() => setActiveTab('unpurchased')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'unpurchased' && styles.activeTabText,
            ]}>
            未购买
          </Text>
          {activeTab === 'unpurchased' && <View style={styles.tabIndicator} />}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabItem,
            activeTab === 'purchased' && styles.activeTabItem,
          ]}
          onPress={() => setActiveTab('purchased')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'purchased' && styles.activeTabText,
            ]}>
            已购买
          </Text>
          {activeTab === 'purchased' && <View style={styles.tabIndicator} />}
        </TouchableOpacity>
      </View>
    );
  };

  const renderProductItem = ({item}) => {
    const createdAtFormatted = dayjs(item.created_at).format('MM-DD');
    const isSelected = selectedProducts[item.id] || false;
    const quantity = productQuantities[item.id] || 1;

    // 获取商品的最新价格和购买价格
    const latestPrice = getLatestPrice(item)?.price;
    const latestPlatform = getLatestPrice(item)?.platform;
    const purchasePrice = getPurchasePrice(item)?.price;
    const purchasePlatform = getPurchasePrice(item)?.platform;

    return (
      <View style={styles.productCardWrapper}>
        <TouchableOpacity
          style={[
            styles.productCard,
            hasSelectedProducts && isSelected && styles.selectedProductCard,
          ]}
          onPress={() => handleProductPress(item.id)}
          activeOpacity={0.8}>

          {/* 商品信息主体 */}
          <View style={styles.productMainInfo}>
            {/* 左侧状态指示器 */}
            <View style={[
              styles.statusIndicator,
              item.purchased ? styles.purchasedIndicator : styles.unpurchasedIndicator,
            ]} />

            {/* 商品内容 */}
            <View style={styles.productContent}>
              {/* 商品名称和状态 */}
              <View style={styles.productHeader}>
                <Text style={styles.productName} numberOfLines={2}>
                  {item.name || '未命名商品'}
                </Text>
                <View style={[
                  styles.statusBadge,
                  item.purchased ? styles.purchasedBadge : styles.unpurchasedBadge,
                ]}>
                  <Text style={styles.statusText}>
                    {item.purchased ? '已购' : '待购'}
                  </Text>
                </View>
              </View>

              {/* 价格信息 */}
              <View style={styles.priceSection}>
                {item.purchased ? (
                  // 已购买商品显示购买价格和当前价格对比
                  <View style={styles.priceComparison}>
                    <View style={styles.priceRow}>
                      <Text style={styles.priceLabel}>购买价</Text>
                      <View style={styles.priceWithPlatform}>
                        <Text style={styles.purchasePrice}>
                          {purchasePrice !== null ? `¥${purchasePrice.toFixed(2)}` : '未记录'}
                        </Text>
                        {purchasePlatform && typeof purchasePlatform === 'string' && purchasePlatform.trim() && (
                          <Text style={styles.platformTag}>{getPlatformLabel(purchasePlatform)}</Text>
                        )}
                      </View>
                    </View>

                    {latestPrice !== null && purchasePrice !== null && (
                      <View style={styles.priceRow}>
                        <Text style={styles.priceLabel}>当前价</Text>
                        <View style={styles.priceWithPlatform}>
                          <Text style={styles.currentPrice}>
                            ¥{latestPrice.toFixed(2)}
                          </Text>
                          {latestPlatform && typeof latestPlatform === 'string' && latestPlatform.trim() && (
                            <Text style={styles.platformTag}>{getPlatformLabel(latestPlatform)}</Text>
                          )}
                        </View>
                      </View>
                    )}

                    {/* 价格差异 */}
                    {purchasePrice !== null && latestPrice !== null && (
                      <View style={styles.priceDiffRow}>
                        <Text style={[
                          styles.priceDiff,
                          latestPrice > purchasePrice ? styles.priceSaved : styles.priceExtra,
                        ]}>
                          {latestPrice > purchasePrice
                            ? `省了 ¥${(latestPrice - purchasePrice).toFixed(2)}`
                            : latestPrice < purchasePrice
                            ? `多花 ¥${(purchasePrice - latestPrice).toFixed(2)}`
                            : '价格持平'}
                        </Text>
                      </View>
                    )}
                  </View>
                ) : (
                  // 未购买商品只显示最新价格
                  <View style={styles.priceRow}>
                    <Text style={styles.priceLabel}>价格</Text>
                    <View style={styles.priceWithPlatform}>
                      <Text style={styles.wishPrice}>
                        {latestPrice !== null ? `¥${latestPrice.toFixed(2)}` : '暂无价格'}
                      </Text>
                      {latestPlatform && typeof latestPlatform === 'string' && latestPlatform.trim() && (
                        <Text style={styles.platformTag}>{getPlatformLabel(latestPlatform)}</Text>
                      )}
                    </View>
                  </View>
                )}
              </View>

              {/* 底部信息 */}
              <View style={styles.productFooter}>
                <Text style={styles.productDate}>{createdAtFormatted}</Text>
                <Icon name="chevron-right" size={12} color="#CCCCCC" />
              </View>
            </View>
          </View>

          {/* 选择模式覆盖层 */}
          {hasSelectedProducts && isSelected && (
            <View style={styles.selectModeOverlay}>
              <CircleCheckbox
                checked={isSelected}
                onChange={() => toggleProductSelection(item.id)}
                style={styles.overlayCheckbox}
              />

              {/* 数量控制器 */}
              <View style={styles.quantityControl}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => decreaseQuantity(item.id)}>
                  <Icon name="minus" size={10} color="#666" />
                </TouchableOpacity>
                <Text style={styles.quantityText}>{quantity}</Text>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => increaseQuantity(item.id)}>
                  <Icon name="plus" size={10} color="#666" />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </TouchableOpacity>

        {/* 非选择模式下的复选框 */}
        {!hasSelectedProducts && (
          <CircleCheckbox
            checked={isSelected}
            onChange={() => toggleProductSelection(item.id)}
            style={styles.normalModeCheckbox}
          />
        )}
      </View>
    );
  };

  // 修改渲染页面右上角按钮
  const renderHeaderRight = () => {
    return (
      <View style={styles.headerRightContainer}>
        <TouchableOpacity style={styles.headerButton} onPress={toggleViewMode}>
          <Icon
            name={viewMode === 'single' ? 'table-cells' : 'bars'}
            size={18}
            color={colors.primary}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleAddProduct}>
          <Icon name="plus" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染搜索框
  const renderSearchInput = () => {
    if (products.length > 1) {
      return (
        <View style={styles.searchInputContainer}>
          <Icon
            name="magnifying-glass"
            size={16}
            color="#999999"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="搜索商品名称"
            placeholderTextColor="#999999"
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchText('')}>
              <Icon name="times-circle" size={16} color="#999999" />
            </TouchableOpacity>
          )}
        </View>
      );
    }
    return null;
  };

  // 修改渲染底部价格条的函数，添加取消按钮
  const renderPriceBar = () => {
    if (!hasSelectedProducts) {
      return null;
    }

    // 计算选中的商品总数量
    const totalQuantity = Object.keys(selectedProducts)
      .filter(id => selectedProducts[Number(id)])
      .reduce((total, id) => total + (productQuantities[Number(id)] || 1), 0);

    return (
      <View style={styles.priceBarContainer}>
        <View style={styles.priceBarHeader}>
          <View style={styles.selectedInfo}>
            {/* 添加全选按钮 */}
            <TouchableOpacity
              style={styles.selectAllButton}
              onPress={toggleSelectAll}
              activeOpacity={0.7}>
              <Icon
                name={isAllSelected ? 'check-square' : 'square'}
                size={16}
                color={colors.primary}
                style={styles.selectAllIcon}
              />
              <Text style={styles.selectAllText}>全选</Text>
            </TouchableOpacity>

            <Text style={styles.selectedCountText}>
              已选 {selectedCount} 件 (共 {totalQuantity} 个)
            </Text>
          </View>

          <View style={styles.actionButtons}>
            {/* 现有删除按钮 */}
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => setShowDeleteConfirm(true)}>
              <Icon name="trash" size={14} color="#FF3B30" />
              <Text style={styles.deleteButtonText}>删除</Text>
            </TouchableOpacity>

            {/* 现有取消按钮 */}
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={clearAllSelections}>
              <Icon name="xmark" size={14} color="#666" />
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.priceInfoContainer}>
          {priceCalculation.hasSelectedPurchasedProduct ? (
            <>
              <View style={styles.priceRow}>
                <View style={styles.priceLabel}>
                  <Text style={styles.priceLabelText}>购买价</Text>
                  {priceCalculation.isBetterWithPurchasePrice && (
                    <View style={styles.economicalTag}>
                      <Text style={styles.economicalTagText}>更划算</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.priceValue}>
                  ¥{priceCalculation.totalWithPurchasePrice}
                </Text>
              </View>

              <View style={styles.priceRow}>
                <View style={styles.priceLabel}>
                  <Text style={styles.priceLabelText}>最新价</Text>
                  {!priceCalculation.isBetterWithPurchasePrice && (
                    <View style={styles.economicalTag}>
                      <Text style={styles.economicalTagText}>更划算</Text>
                    </View>
                  )}
                </View>
                <Text style={[styles.priceValue, styles.latestTotalPrice]}>
                  ¥{priceCalculation.totalWithLatestPrice}
                </Text>
              </View>

              <View style={styles.diffRow}>
                <Text style={styles.diffText}>
                  差值: ¥{priceCalculation.difference}
                  <Text style={styles.betterText}>
                    {priceCalculation.isBetterWithPurchasePrice
                      ? ' (购买价更便宜)'
                      : ' (当前价更便宜)'}
                  </Text>
                </Text>
              </View>
            </>
          ) : (
            <View style={styles.priceRow}>
              <Text style={styles.priceLabelText}>总计</Text>
              <Text style={styles.priceValue}>
                ¥{priceCalculation.totalWithLatestPrice}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染删除确认对话框
  const renderDeleteConfirmDialog = () => {
    if (!showDeleteConfirm) {return null;}

    return (
      <View style={styles.confirmDialogOverlay}>
        <View style={styles.confirmDialog}>
          <Text style={styles.confirmDialogTitle}>删除确认</Text>
          <Text style={styles.confirmDialogText}>
            确定要删除选中的 {selectedCount} 件商品吗？此操作不可恢复。
          </Text>

          <View style={styles.confirmDialogButtons}>
            <TouchableOpacity
              style={[styles.confirmDialogButton, styles.cancelDialogButton]}
              onPress={() => setShowDeleteConfirm(false)}>
              <Text style={styles.cancelDialogButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.confirmDialogButton, styles.confirmDialogButtonDanger]}
              onPress={handleBatchDelete}>
              <Text style={styles.confirmDialogButtonDangerText}>删除</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <PageContainer
      headerTitle="愿望清单"
      backgroundColor="#F5F7FA"
      rightComponent={renderHeaderRight()}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      ) : (
        <View style={styles.container}>
          {renderSearchInput()}

          {renderTabBar()}

          {filteredProducts.length > 0 ? (
            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={item => item.id.toString()}
              numColumns={viewMode === 'single' ? 1 : 2} // 根据视图模式设置列数
              contentContainerStyle={[
                styles.listContainer,
                products.length > 1 ? {paddingTop: 8} : {},
                hasSelectedProducts &&
                priceCalculation.hasSelectedPurchasedProduct
                  ? {paddingBottom: 140} // 增加底部内边距，适应带差值显示的价格条
                  : hasSelectedProducts
                  ? {paddingBottom: 90}
                  : {paddingBottom: 20},
              ]}
              key={viewMode} // 当视图模式变化时重新渲染列表
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <EmptyState
              icon={searchText ? 'search' : 'box'}
              title={searchText ? '没有找到结果' : '暂无商品'}
              message={
                searchText
                  ? '尝试使用其他关键词搜索'
                  : '添加想要购买或者已购买的商品，追踪价格变化'
              }
              actionText={searchText ? undefined : '添加商品'}
              onAction={searchText ? undefined : handleAddProduct}
            />
          )}

          {renderPriceBar()}
          {renderDeleteConfirmDialog()}
        </View>
      )}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 标签栏样式 - 优化更小巧美观
  tabBarContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 12,
    marginBottom: 8,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
    height: 40,
    overflow: 'hidden',
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  activeTabItem: {
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  activeTabText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '25%',
    right: '25%',
    height: 2,
    backgroundColor: COLORS.primary,
    borderTopLeftRadius: 2,
    borderTopRightRadius: 2,
  },
  // 搜索框
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    paddingHorizontal: 12,
    height: 40,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    height: 40,
    color: '#333333',
    padding: 0,
  },
  clearButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#999999',
  },
  listContainer: {
    paddingHorizontal: 8,
    paddingBottom: 80, // 为底部按钮留出空间
  },
  // 优化后的商品卡片样式 - 去除图片依赖
  productCardWrapper: {
    marginHorizontal: 16,
    marginVertical: 6,
    position: 'relative',
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  productMainInfo: {
    flexDirection: 'row',
    padding: 16,
  },
  statusIndicator: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  purchasedIndicator: {
    backgroundColor: '#4CD964',
  },
  unpurchasedIndicator: {
    backgroundColor: '#007AFF',
  },
  productContent: {
    flex: 1,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    marginRight: 12,
    lineHeight: 22,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 50,
    alignItems: 'center',
  },
  purchasedBadge: {
    backgroundColor: '#E8F5E8',
  },
  unpurchasedBadge: {
    backgroundColor: '#E3F2FD',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  purchasedText: {
    color: '#4CD964',
  },
  unpurchasedText: {
    color: '#007AFF',
  },
  priceSection: {
    marginBottom: 12,
  },
  priceComparison: {
    // 价格对比容器
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  priceLabel: {
    fontSize: 13,
    color: '#666666',
    fontWeight: '500',
    minWidth: 50,
  },
  priceWithPlatform: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },
  purchasePrice: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginRight: 8,
  },
  currentPrice: {
    fontSize: 15,
    fontWeight: '600',
    color: '#FF9500',
    marginRight: 8,
  },
  wishPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FF3B30',
    marginRight: 8,
  },
  platformTag: {
    fontSize: 11,
    color: '#666666',
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priceDiffRow: {
    alignItems: 'flex-end',
    marginTop: 4,
  },
  priceDiff: {
    fontSize: 12,
    fontWeight: '500',
  },
  priceSaved: {
    color: '#4CD964',
  },
  priceExtra: {
    color: '#FF3B30',
  },
  productFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productDate: {
    fontSize: 11,
    color: '#999999',
  },
  normalModeCheckbox: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  // 底部价格条样式更新
  priceBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  priceBarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
  },
  selectedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedIcon: {
    marginRight: 6,
  },
  selectedCountText: {
    fontSize: 13,
    color: '#666666',
  },
  priceInfoContainer: {
    width: '100%',
  },
  priceLabelText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '600',
  },
  priceValue: {
    fontSize: 18,
    color: '#FF3B30',
    fontWeight: '700',
  },
  latestTotalPrice: {
    fontSize: 16,
    color: '#FF9500', // 使用橙色区分最新价格
  },
  economicalTag: {
    backgroundColor: '#4CD964',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  economicalTagText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  diffRow: {
    marginTop: 6,
    alignItems: 'flex-end',
  },
  diffText: {
    fontSize: 12,
    color: '#666666',
  },
  betterText: {
    fontWeight: '500',
    color: '#4CD964',
  },
  // 浮动按钮
  floatingAddButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  // 自定义圆形复选框样式
  circleCheckbox: {
    position: 'absolute',
    top: 8,
    left: 8,
    zIndex: 10,
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  circleCheckboxChecked: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary,
  },
  checkbox: {
    // 给自定义复选框的额外样式
  },
  priceIncrease: {
    color: '#FF3B30',
  },
  priceDecrease: {
    color: '#4CD964',
  },
  noPrice: {
    color: '#999999',
  },
  selectedProductCard: {
    borderColor: COLORS.primary,
    borderWidth: 2,
  },
  selectModeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    flexDirection: 'column',
  },
  overlayCheckbox: {
    position: 'relative',
    top: 0,
    left: 0,
    width: 28, // 稍微大一点
    height: 28,
    borderRadius: 14,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: '#F0F0F0',
  },
  cancelButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 3,
  },
  singleModeCheckbox: {
    position: 'absolute',
    top: 8,
    left: 24,
    zIndex: 10,
  },
  // 添加的样式
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: '#FFEEEE',
    marginRight: 8,
  },
  deleteButtonText: {
    fontSize: 12,
    color: '#FF3B30',
    marginLeft: 3,
    fontWeight: '500',
  },
  // 确认对话框样式
  confirmDialogOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  confirmDialog: {
    width: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 10,
  },
  confirmDialogTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmDialogText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  confirmDialogButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmDialogButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelDialogButton: {
    backgroundColor: '#F0F0F0',
    marginRight: 10,
  },
  cancelDialogButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  confirmDialogButtonDanger: {
    backgroundColor: '#FF3B30',
  },
  confirmDialogButtonDangerText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  // 添加数量控制相关样式
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 6,
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quantityButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    paddingHorizontal: 12,
    minWidth: 30,
    textAlign: 'center',
  },
  // 添加全选按钮样式
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.08)',
    marginRight: 8,
  },
  selectAllIcon: {
    marginRight: 4,
  },
  selectAllText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
  },
});

export default ProductListScreen;
