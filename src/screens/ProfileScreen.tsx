import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  InteractionManager,
  AppState,
  TextInput,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {Switch as AndSwitch} from '@ant-design/react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';

import PageContainer from '../components/PageContainer';
import databaseService, {BudgetSettings} from '../services/DatabaseService';
import CustomModal from '../components/CustomModal';
import ConfirmDialog from '../components/ConfirmDialog';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'; // 导入中文语言包
import {shareFile, getFileFromDocuments, getBackupFileFromDocuments} from '../utils/utils';
import {COLORS} from '../utils/color';
import PaymentDetectionService from '../services/PaymentDetectionService';
// 导入 useToast 钩子
import {useToast} from '../context/ToastContext';
import {useFocusEffect} from '@react-navigation/native';
import {useTheme} from '../context/ThemeContext';
import {calculateDailyFortune} from '../utils/algorithm';
import AsyncStorage from '@react-native-async-storage/async-storage';
// 设置 dayjs 使用中文
dayjs.locale('zh-cn');

const ProfileScreen = ({navigation}) => {
  const {colors} = useTheme();
  // 使用 useToast 钩子获取 showToast 函数
  const {showToast} = useToast();

  const [isLoading, setIsLoading] = useState(false);
  const [importDialogVisible, setImportDialogVisible] = useState(false);
  const [importData, setImportData] = useState<any>(null);
  const [autoRecordingEnabled, setAutoRecordingEnabled] = useState(false);
  const [permissionsModalVisible, setPermissionsModalVisible] = useState(false);
  const [accessibilityEnabled, setAccessibilityEnabled] = useState(false);
  const [notificationEnabled, setNotificationEnabled] = useState(false);
  const [overlayEnabled, setOverlayEnabled] = useState(false);
  const [importSourceModalVisible, setImportSourceModalVisible] =
    useState(false);
  const [importSource, setImportSource] = useState('');
  const [accountStats, setAccountStats] = useState({
    firstRecordDate: '',
    totalDays: 0,
    totalRecords: 0,
    todayDate: dayjs().format('YYYY年MM月DD日'),
  });

  // 运势相关状态
  const [fortuneData, setFortuneData] = useState<any>(null);
  const [showFortune, setShowFortune] = useState(false);
  const [fortuneExpanded, setFortuneExpanded] = useState(false);

  // 账户统计折叠状态
  const [statsExpanded, setStatsExpanded] = useState(true);

  // 添加确认对话框的状态
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [confirmModalTitle, setConfirmModalTitle] = useState('');
  const [confirmModalMessage, setConfirmModalMessage] = useState('');
  const [confirmModalAction, setConfirmModalAction] = useState(() => {});

  // 数据恢复相关状态
  const [restoreData, setRestoreData] = useState<any>(null);
  const [restoreConfirmVisible, setRestoreConfirmVisible] = useState(false);
  // 添加确认对话框状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );

  // 添加 AppState 监听
  const [appState, setAppState] = useState(AppState.currentState);

  // 添加预算设置相关状态
  const [budgetModalVisible, setBudgetModalVisible] = useState(false);
  const [budgetSettings, setBudgetSettings] = useState<BudgetSettings>({
    daily: '',
    monthly: '',
    yearly: '',
    enabled: false,
  });

  // 添加自动导出设置状态
  const [autoExportSettings, setAutoExportSettings] = useState({
    enabled: false,
    lastExportDate: undefined,
  });

  // 添加加载弹窗状态
  const [loadingModalVisible, setLoadingModalVisible] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');

  // 监听应用状态变化
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      // 当应用从后台回到前台时，检查权限状态
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        console.log('应用从后台回到前台，检查权限状态');
        checkPermissions();
      }
      setAppState(nextAppState);
    });

    return () => {
      subscription.remove();
    };
  }, [appState]);

  // 当页面获得焦点时检查权限状态
  useFocusEffect(
    useCallback(() => {
      console.log('个人中心页面获得焦点，检查权限状态');
      checkPermissions();
      loadBudgetSettings(); // 加载预算设置
      loadAutoExportSettings(); // 加载自动导出设置
      return () => {
        // 清理函数（如果需要）
      };
    }, []),
  );

  // 加载账户统计数据和权限状态
  useEffect(() => {
    // 使用 InteractionManager 延迟加载，提高页面切换流畅度
    InteractionManager.runAfterInteractions(() => {
      loadAccountStats();
      checkPermissions();
      loadBudgetSettings(); // 加载预算设置
      loadAutoExportSettings(); // 加载自动导出设置
      loadFortuneData(); // 加载运势数据
    });
  }, []);

  // 加载自动导出设置
  const loadAutoExportSettings = async () => {
    try {
      const settings = await databaseService.getAutoExportSettings();
      if (settings) {
        setAutoExportSettings(settings);
      }
    } catch (error) {
      console.error('加载自动导出设置失败', error);
    }
  };

  // 加载运势数据
  const loadFortuneData = async () => {
    try {
      const savedUserInfo = await AsyncStorage.getItem('fortuneUserInfo');
      if (savedUserInfo) {
        const userInfoData = JSON.parse(savedUserInfo);
        const fortune = calculateDailyFortune(userInfoData);

        if (fortune && fortune.overall >= 70) {
          // 只有运势较好时才显示
          setFortuneData(fortune);
          setShowFortune(true);
        } else {
          setShowFortune(false);
        }
      } else {
        setShowFortune(false);
      }
    } catch (error) {
      console.error('加载运势数据失败:', error);
      setShowFortune(false);
    }
  };

  // 检查权限状态
  const checkPermissions = async () => {
    try {
      console.log('正在检查权限状态...');
      const accessibilityPermission =
        await PaymentDetectionService.checkAccessibilityPermission();
      const notificationPermission =
        await PaymentDetectionService.checkNotificationPermission();

      // 检查悬浮窗权限 - 添加错误处理
      let overlayPermission = false;
      try {
        overlayPermission =
          await PaymentDetectionService.checkOverlayPermission();
      } catch (error) {
        console.error('检查悬浮窗权限失败，默认为 false', error);
        // 暂时默认为 true，以便测试其他功能
        overlayPermission = true;
      }

      console.log('权限状态:', {
        accessibility: accessibilityPermission,
        notification: notificationPermission,
        overlay: overlayPermission,
      });

      // 更新权限状态
      setAccessibilityEnabled(accessibilityPermission);
      setNotificationEnabled(notificationPermission);
      setOverlayEnabled(overlayPermission);

      // 更新自动记账状态
      const autoRecordingStatus =
        accessibilityPermission && notificationPermission;
      setAutoRecordingEnabled(autoRecordingStatus);

      // 如果权限都已开启，启动监听
      if (autoRecordingStatus) {
        PaymentDetectionService.startListening();
      } else {
        PaymentDetectionService.stopListening();
      }
    } catch (error) {
      console.error('检查权限失败', error);
      showToast('检查权限状态失败', 'error');
    }
  };

  // 打开自动记账设置
  const openAutoRecordingSettings = () => {
    setPermissionsModalVisible(true);
  };

  // 处理无障碍权限开关变化
  const handleAccessibilityToggle = value => {
    if (value) {
      PaymentDetectionService.openAccessibilitySettings();
    } else {
      setConfirmDialogMessage('请在系统设置中手动关闭无障碍权限');
      setConfirmDialogAction(() => () => {
        PaymentDetectionService.openAccessibilitySettings();
      });
      setConfirmDialogVisible(true);
    }
  };

  // 处理通知权限开关变化
  const handleNotificationToggle = value => {
    if (value) {
      PaymentDetectionService.openNotificationSettings();
    } else {
      setConfirmDialogMessage('请在系统设置中手动关闭通知权限');
      setConfirmDialogAction(() => () => {
        PaymentDetectionService.openNotificationSettings();
      });
      setConfirmDialogVisible(true);
    }
  };

  // 处理悬浮窗权限开关变化
  const handleOverlayToggle = value => {
    if (value) {
      try {
        PaymentDetectionService.openOverlaySettings();
      } catch (error) {
        console.error('打开悬浮窗设置失败', error);
        setConfirmDialogMessage(
          '悬浮窗权限设置功能暂时不可用，请手动在系统设置中开启。',
        );
        setConfirmDialogAction(() => () => {
          setConfirmDialogVisible(false);
        });
        setConfirmDialogVisible(true);
      }
    } else {
      setConfirmDialogMessage('请在系统设置中手动关闭悬浮窗权限');
      setConfirmDialogAction(() => () => {
        PaymentDetectionService.openOverlaySettings();
      });
      setConfirmDialogVisible(true);
    }
  };

  // 关闭权限弹窗并检查权限
  const handleClosePermissionsModal = () => {
    setPermissionsModalVisible(false);
    // 立即检查一次权限
    checkPermissions();

    // 延迟再检查一次权限，以防系统设置页面返回后状态尚未更新
    setTimeout(() => {
      checkPermissions();
    }, 1000);
  };

  // 加载账户统计数据
  const loadAccountStats = useCallback(async () => {
    setIsLoading(true);
    try {
      // 获取所有交易记录
      const transactions = await databaseService.getAllTransactions();
      console.log('transactions', transactions);
      // 如果没有交易记录
      if (transactions.length === 0) {
        setAccountStats({
          firstRecordDate: '尚未记账',
          totalDays: 0,
          totalRecords: 0,
          todayDate: dayjs().format('YYYY年MM月DD日'),
        });
        setIsLoading(false);
        return;
      }

      // 按日期排序
      transactions.sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
      );

      // 获取第一笔记录的日期
      const firstDate = dayjs(transactions[0].date);
      const firstRecordDate = firstDate.format('YYYY年MM月DD日');

      // 计算记账总天数
      const today = dayjs();
      const diffDays = today.diff(firstDate, 'day') + 1;

      // 设置统计数据
      setAccountStats({
        firstRecordDate,
        totalDays: diffDays,
        totalRecords: transactions.length,
        todayDate: today.format('YYYY年MM月DD日'),
      });
    } catch (error) {
      console.error('加载账户统计数据失败', error);
      showToast('加载账户统计数据失败', 'error');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 数据备份函数
  const handleDataBackup = useCallback(async () => {
    setLoadingMessage('正在备份数据...');
    setLoadingModalVisible(true);

    try {
      // 获取其他数据（不包括账单、分类、账本，这些有专门的导出功能）
      const [
        creditCards,
        installmentPlans,
        loanRecords,
        products
      ] = await Promise.all([
        databaseService.getAllCreditCards(),
        databaseService.getInstallmentPlans(),
        databaseService.getAllLoanRecords(),
        databaseService.getAllProducts()
      ]);

      // 获取商品价格数据
      const productsWithPrices = await Promise.all(
        products.map(async (product) => {
          if (product.id) {
            const prices = await databaseService.getProductPrices(product.id);
            return { ...product, prices };
          }
          return { ...product, prices: [] };
        })
      );

      // 创建其他数据的备份（不包括账单数据）
      const backupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        dataType: 'other', // 标识这是其他数据备份，不是账单数据
        data: {
          creditCards,
          installmentPlans,
          loanRecords,
          products: productsWithPrices
        }
      };

      // 生成文件名
      const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
      const fileName = `雪球记账_其他数据备份_${timestamp}.json`;

      // 分享文件
      await shareFile(JSON.stringify(backupData, null, 2), fileName);

      showToast('数据备份成功', 'success');
    } catch (error) {
      console.error('数据备份失败:', error);
      showToast('数据备份失败', 'error');
    } finally {
      setLoadingModalVisible(false);
    }
  }, []);

  // 数据恢复函数
  const handleDataRestore = useCallback(async () => {
    try {
      setLoadingMessage('正在选择备份文件...');
      setLoadingModalVisible(true);

      // 使用专门的备份文件选择函数
      const result = await getBackupFileFromDocuments();

      setLoadingModalVisible(false);

      if (result.error) {
        // 显示错误信息
        showToast(result.error, 'error');
        return;
      }

      // 成功获取备份数据
      setRestoreData(result);
      setRestoreConfirmVisible(true);
    } catch (error) {
      setLoadingModalVisible(false);
      console.error('选择备份文件失败:', error);
      showToast('选择备份文件失败', 'error');
    }
  }, []);

  // 确认数据恢复
  const confirmDataRestore = useCallback(async () => {
    if (!restoreData) return;

    setRestoreConfirmVisible(false);
    setLoadingMessage('正在恢复数据...');
    setLoadingModalVisible(true);

    try {
      const { data } = restoreData;
      let restoredCount = 0;
      // 恢复信用卡
      if (data.creditCards && data.creditCards.length > 0) {
        for (const card of data.creditCards) {
          try {
            await databaseService.addCreditCard({
              bankName: card.bankName || card.bank || '未知银行',
              lastThreeDigits: card.lastThreeDigits || '000',
              billingDay: card.billingDay || card.billDate || 1,
              paymentDueDay: card.paymentDueDay || card.dueDate || 20,
              color: card.color
            });
            restoredCount++;
          } catch (error) {
            console.error('恢复信用卡失败:', error);
          }
        }
      }

      // 恢复借款记录
      if (data.loanRecords && data.loanRecords.length > 0) {
        for (const loan of data.loanRecords) {
          try {
            await databaseService.addLoanRecord({
              type: loan.type,
              amount: loan.amount,
              note: loan.note,
              loanDate: loan.loanDate || loan.date,
              repayments: loan.repayments || []
            });
            restoredCount++;
          } catch (error) {
            console.error('恢复借款记录失败:', error);
          }
        }
      }

      // 恢复愿望清单
      if (data.products && data.products.length > 0) {
        for (const product of data.products) {
          try {
            const productId = await databaseService.saveProduct({
              name: product.name,
              thumbnail: product.thumbnail || product.imageUrl,
              notes: product.notes || product.description,
              createdAt: product.createdAt || new Date().toISOString(),
              updatedAt: product.updatedAt || new Date().toISOString(),
              purchased: product.purchased || (product.isPurchased ? 1 : 0)
            });

            // 恢复价格历史
            if (product.prices && product.prices.length > 0) {
              for (const price of product.prices) {
                try {
                  await databaseService.addProductPrice({
                    productId: productId,
                    price: price.price,
                    platform: price.platform || '未知平台',
                    isPurchasePrice: price.isPurchasePrice || false,
                    createdAt: price.createdAt || price.date || new Date().toISOString(),
                    updatedAt: price.updatedAt || price.date || new Date().toISOString()
                  });
                } catch (error) {
                  console.error('恢复价格历史失败:', error);
                }
              }
            }
            restoredCount++;
          } catch (error) {
            console.error('恢复商品失败:', error);
          }
        }
      }

      showToast(`数据恢复成功，共恢复 ${restoredCount} 项数据`, 'success');

      // 重新加载数据
      loadAccountStats();
    } catch (error) {
      console.error('数据恢复失败:', error);
      showToast('数据恢复失败', 'error');
    } finally {
      setLoadingModalVisible(false);
      setRestoreData(null);
    }
  }, [restoreData]);

  // 修改导出数据函数 - 支持智能分批处理
  const handleExportData = useCallback(async () => {
    // 显示加载弹窗
    setLoadingMessage('正在准备导出数据...');
    setLoadingModalVisible(true);

    try {
      // 获取交易记录总数
      const totalCount = await databaseService.getTransactionCount();
      console.log('总交易记录数:', totalCount);

      if (totalCount === 0) {
        showToast('没有数据可以导出', 'warning');
        return;
      }

      // 动态确定批处理大小
      const batchSize = totalCount > 5000 ? 1000 : Math.min(totalCount, 500);
      const shouldUseBatch = totalCount > 2000;

      console.log(
        `导出配置: 总记录=${totalCount}, 批大小=${batchSize}, 使用分批=${shouldUseBatch}`,
      );

      // 创建 CSV 内容
      let csvContent =
        '日期,类型,金额,分类,家庭账单id,家庭账单,账本id,账本,备注\n';
      let processedCount = 0;

      if (shouldUseBatch) {
        // 分批处理大量数据
        const totalBatches = Math.ceil(totalCount / batchSize);

        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
          const offset = batchIndex * batchSize;

          // 更新进度提示
          const progress = Math.round(((batchIndex + 1) / totalBatches) * 100);
          setLoadingMessage(
            `正在导出数据... ${progress}% (${processedCount}/${totalCount})`,
          );

          // 获取当前批次的交易记录
          const transactions = await databaseService.getTransactionsPaginated(
            offset,
            batchSize,
          );

          // 处理当前批次的数据
          transactions.forEach(transaction => {
            const type = transaction.type === 'expense' ? '支出' : '收入';
            const date = transaction.date
              ? dayjs(transaction.date).format('YYYY-MM-DD')
              : '';
            const amount = transaction.amount || 0;
            const categoryName = transaction.categoryName || '未知分类';
            const note = transaction.note || '';
            const familyName = transaction.familyName || '';
            const familyId = transaction.familyId || '';
            const accountBookId = transaction.accountBookId || 1;
            const accountBookName = transaction.accountBookName || '默认账本';

            // 处理 CSV 中的特殊字符
            const escapedNote = note.replace(/"/g, '""');
            csvContent += `${date},${type},${amount},${categoryName},${familyId},${familyName},${accountBookId},${accountBookName},"${escapedNote}"\n`;
          });

          processedCount += transactions.length;

          // 给UI一个更新的机会，避免阻塞
          if (batchIndex < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      } else {
        // 小数据量直接处理
        setLoadingMessage('正在导出数据...');
        const transactions = await databaseService.getAllTransactions(
          undefined,
        );

        transactions.forEach(transaction => {
          const type = transaction.type === 'expense' ? '支出' : '收入';
          const date = transaction.date
            ? dayjs(transaction.date).format('YYYY-MM-DD')
            : '';
          const amount = transaction.amount || 0;
          const categoryName = transaction.categoryName || '未知分类';
          const note = transaction.note || '';
          const familyName = transaction.familyName || '';
          const familyId = transaction.familyId || '';
          const accountBookId = transaction.accountBookId || 1;
          const accountBookName = transaction.accountBookName || '默认账本';

          // 处理 CSV 中的特殊字符
          const escapedNote = note.replace(/"/g, '""');
          csvContent += `${date},${type},${amount},${categoryName},${familyId},${familyName},${accountBookId},${accountBookName},"${escapedNote}"\n`;
        });

        processedCount = transactions.length;
      }

      // 最终进度更新
      setLoadingMessage('正在生成文件...');

      // 分享文件
      await shareFile(csvContent, '雪球记账');

      // 显示成功提示
      showToast(`数据导出成功，共导出 ${processedCount} 条记录`, 'success');
    } catch (err) {
      console.error('导出数据失败', err);

      // 安全地处理错误消息
      let errorMessage = '导出数据失败';
      if (err) {
        if (typeof err === 'object') {
          if ('message' in err) {
            errorMessage += ': ' + err.message;
          } else {
            errorMessage += ': ' + JSON.stringify(err);
          }
        } else {
          errorMessage += ': ' + String(err);
        }
      }
      showToast(errorMessage || '错误', 'error');
    } finally {
      // 隐藏加载弹窗
      setLoadingModalVisible(false);
    }
  }, []);

  // 添加导入源选择弹窗组件
  const renderImportSourceModal = () => (
    <CustomModal
      visible={importSourceModalVisible}
      onClose={() => setImportSourceModalVisible(false)}
      position="center"
      animationType="fade">
      <View style={styles.importSourceModalContainer}>
        <Text style={styles.importSourceModalTitle}>选择导入数据源</Text>
        <Text style={styles.importSourceModalSubtitle}>
          请选择您要导入的数据来源
        </Text>

        <TouchableOpacity
          style={styles.importSourceOption}
          onPress={() => handleImportData('miaomiao')}>
          <Icon
            name="cat"
            size={24}
            color="#FF9500"
            style={styles.importSourceIcon}
          />
          <View style={styles.importSourceTextContainer}>
            <Text style={styles.importSourceName}>喵喵记账</Text>
            <Text style={styles.importSourceDescription}>
              导入喵喵记账导出的JSON数据
            </Text>
          </View>
          <Icon name="chevron-right" size={16} color="#CCCCCC" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.importSourceOption}
          onPress={() => handleImportData('snowball')}>
          <Icon
            name="snowflake"
            size={24}
            color="#007AFF"
            style={styles.importSourceIcon}
          />
          <View style={styles.importSourceTextContainer}>
            <Text style={styles.importSourceName}>雪球记账</Text>
            <Text style={styles.importSourceDescription}>
              导入雪球记账导出的JSON数据
            </Text>
          </View>
          <Icon name="chevron-right" size={16} color="#CCCCCC" />
        </TouchableOpacity>
      </View>
    </CustomModal>
  );

  // 修改导入数据函数
  const handleImportData = async source => {
    setImportSource(source);
    setImportSourceModalVisible(false);
    try {
      // 获取 JSON 数据
      const data = await getFileFromDocuments(source);

      // 隐藏加载弹窗
      setLoadingModalVisible(false);

      if (data.error) {
        // 如果有错误信息，显示错误信息
        showToast(data.error, 'error');
        return;
      }

      if (!data || !data.transactions || !data.transactions.length) {
        showToast('未能获取有效的导入数据', 'error');
        return;
      }

      // 设置导入数据并显示确认对话框
      setImportData(data);
      setImportDialogVisible(true);
    } catch (err) {
      console.error('导入数据失败', err);

      // 安全地处理错误消息
      let errorMessage = '导入数据失败';
      if (err) {
        if (typeof err === 'object') {
          if ('message' in err) {
            errorMessage += ': ' + err.message;
          } else {
            errorMessage += ': ' + JSON.stringify(err);
          }
        } else {
          errorMessage += ': ' + String(err);
        }
      }
      showToast(errorMessage || '错误', 'error');
    } finally {
      // 确保加载弹窗被隐藏
      setLoadingModalVisible(false);
    }
  };

  // 提取并保存备注历史的辅助函数
  const extractAndSaveNoteHistory = async (transactions: any[]) => {
    try {
      // 收集所有有备注的交易记录
      const notesMap = new Map<string, Set<string>>();

      for (const transaction of transactions) {
        if (
          transaction.note &&
          transaction.note.trim() &&
          transaction.categoryId
        ) {
          const categoryId = transaction.categoryId;
          const note = transaction.note.trim();

          if (!notesMap.has(categoryId)) {
            notesMap.set(categoryId, new Set());
          }
          notesMap.get(categoryId)!.add(note);
        }
      }

      // 批量保存备注历史
      let savedNotesCount = 0;
      for (const [categoryId, notes] of notesMap) {
        for (const note of notes) {
          try {
            await databaseService.saveCategoryNote(categoryId, note);
            savedNotesCount++;
          } catch (error) {
            console.error(`保存备注历史失败: ${note}`, error);
          }
        }
      }

      console.log(
        `已保存 ${savedNotesCount} 条备注历史到 ${notesMap.size} 个分类`,
      );

      // 返回统计信息
      return {
        totalNotes: savedNotesCount,
        categoriesCount: notesMap.size,
      };
    } catch (error) {
      console.error('提取备注历史失败:', error);
      return {totalNotes: 0, categoriesCount: 0};
    }
  };

  // 修改确认导入数据函数
  const confirmImportData = async () => {
    setImportDialogVisible(false);

    // 显示加载弹窗
    setLoadingMessage('正在导入数据...');
    setLoadingModalVisible(true);

    try {
      // 获取所有分类
      const categories = await databaseService.getAllCategories();

      // 创建分类名称到ID的映射
      const categoryNameToIdMap = {};
      categories.forEach(category => {
        // 使用 "分类名称_类型" 作为键，以区分同名但类型不同的分类
        const key = `${category.name}_${
          category.isExpense ? 'expense' : 'income'
        }`;
        categoryNameToIdMap[key] = category.id;
      });

      // 导入交易记录
      let importedCount = 0;
      console.log('----importData', importData);

      // 收集需要创建的新分类
      const newCategoriesToCreate = new Map();
      // 收集需要创建的新账本
      const newAccountBooksToCreate = new Map();

      // 第一遍扫描：收集所有需要创建的分类和账本
      for (const transaction of importData.transactions) {
        // 确定交易类型
        const isExpense = transaction.type === 'expense';

        // 如果有分类名称但在现有分类中找不到，则需要创建
        if (transaction.category) {
          // 构建查找键
          const lookupKey = `${transaction.category}_${
            isExpense ? 'expense' : 'income'
          }`;

          // 检查是否存在此分类
          if (
            !categoryNameToIdMap[lookupKey] &&
            !newCategoriesToCreate.has(lookupKey)
          ) {
            // 将新分类添加到待创建列表
            newCategoriesToCreate.set(lookupKey, {
              name: transaction.category,
              isExpense: isExpense,
              icon: 'snowflake', // 统一使用 snowflake 图标
            });
          }
        }

        // 如果有账本名称，检查是否需要创建新账本
        if (
          transaction.accountBookName &&
          transaction.accountBookName !== '默认账本'
        ) {
          if (!newAccountBooksToCreate.has(transaction.accountBookName)) {
            // 将新账本添加到待创建列表
            newAccountBooksToCreate.set(transaction.accountBookName, {
              name: transaction.accountBookName,
              description: `从导入数据自动创建的账本`,
            });
          }
        }
      }

      // 创建所有新分类
      for (const [key, categoryData] of newCategoriesToCreate.entries()) {
        try {
          const newCategoryId = await databaseService.addCategory(categoryData);
          console.log(
            `创建了新分类: ${categoryData.name}, ID: ${newCategoryId}`,
          );

          // 将新创建的分类添加到映射中
          categoryNameToIdMap[key] = newCategoryId;
        } catch (error) {
          console.error(`创建分类 ${categoryData.name} 失败:`, error);
        }
      }

      // 创建所有新账本
      const accountBookNameToIdMap = new Map();
      for (const [
        accountBookName,
        accountBookData,
      ] of newAccountBooksToCreate) {
        try {
          const newAccountBook =
            await databaseService.createAccountBookIfNotExists(
              accountBookData.name,
              accountBookData.description,
            );
          // 更新映射表
          accountBookNameToIdMap.set(accountBookName, newAccountBook.id);
          console.log(
            `创建/获取账本: ${accountBookData.name} (ID: ${newAccountBook.id})`,
          );
        } catch (error) {
          console.error(`创建账本失败: ${accountBookData.name}`, error);
        }
      }

      // 第二遍扫描：准备所有交易记录数据
      const transactionsToImport: any[] = [];

      for (const transaction of importData.transactions) {
        // 确定交易类型
        const isExpense = transaction.type === 'expense';

        // 尝试查找匹配的分类
        let categoryId: string | null = null;
        let categoryName =
          transaction.category || (isExpense ? '其他支出' : '其他收入');

        if (categoryName) {
          // 构建查找键
          const lookupKey = `${categoryName}_${
            isExpense ? 'expense' : 'income'
          }`;

          // 查找分类ID（包括新创建的分类）
          categoryId = categoryNameToIdMap[lookupKey] || null;
        }

        // 如果仍然找不到匹配的分类（极少数情况），使用"其他"分类
        if (!categoryId) {
          const otherKey = `其他_${isExpense ? 'expense' : 'income'}`;
          categoryId = categoryNameToIdMap[otherKey] || null;

          // 如果连"其他"分类都找不到，则创建它
          if (!categoryId) {
            try {
              const newCategoryId = await databaseService.addCategory({
                name: '其他',
                isExpense: isExpense,
                icon: 'snowflake',
              });
              categoryId = newCategoryId;
              categoryNameToIdMap[otherKey] = newCategoryId;
            } catch (error) {
              console.error('创建"其他"分类失败:', error);
              // 如果创建失败，使用一个默认值
              categoryId = '1';
            }
          }
        }

        // 处理账本信息
        let finalAccountBookId = 1; // 默认账本ID
        let finalAccountBookName = '默认账本';

        if (
          transaction.accountBookName &&
          transaction.accountBookName !== '默认账本'
        ) {
          // 如果有指定的账本名称，尝试从映射表中获取ID
          const mappedAccountBookId = accountBookNameToIdMap.get(
            transaction.accountBookName,
          );
          if (mappedAccountBookId) {
            finalAccountBookId = mappedAccountBookId;
            finalAccountBookName = transaction.accountBookName;
          } else {
            // 如果映射表中没有，尝试从数据库中查找
            try {
              const existingBook = await databaseService.getAccountBookByName(
                transaction.accountBookName,
              );
              if (existingBook) {
                finalAccountBookId = existingBook.id!;
                finalAccountBookName = existingBook.name;
              }
            } catch (error) {
              console.error('查找账本失败:', error);
            }
          }
        } else if (
          transaction.accountBookId &&
          transaction.accountBookId !== 1
        ) {
          // 如果有指定的账本ID，使用原有的ID和名称
          finalAccountBookId = transaction.accountBookId;
          finalAccountBookName = transaction.accountBookName || '默认账本';
        }

        // 准备交易记录数据
        transactionsToImport.push({
          date: transaction.date,
          type: transaction.type,
          amount: parseFloat(transaction.amount),
          categoryId: categoryId || '1',
          categoryName: categoryName, // 使用原始分类名称
          note: transaction.note || '',
          familyId: transaction.familyId || '',
          familyName: transaction.familyName || '',
          accountBookId: finalAccountBookId,
          accountBookName: finalAccountBookName,
          isReimbursable: false, // 导入的数据默认不需要报销
        });
      }

      // 分批导入交易记录
      const totalTransactions = transactionsToImport.length;
      const batchSize =
        totalTransactions > 1000 ? 500 : Math.min(totalTransactions, 200);
      const shouldUseBatch = totalTransactions > 100;

      console.log(
        `导入配置: 总记录=${totalTransactions}, 批大小=${batchSize}, 使用分批=${shouldUseBatch}`,
      );

      if (shouldUseBatch) {
        // 分批处理大量数据
        const totalBatches = Math.ceil(totalTransactions / batchSize);

        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
          const startIndex = batchIndex * batchSize;
          const endIndex = Math.min(startIndex + batchSize, totalTransactions);
          const batchTransactions = transactionsToImport.slice(
            startIndex,
            endIndex,
          );

          // 更新进度提示
          const progress = Math.round(((batchIndex + 1) / totalBatches) * 100);
          setLoadingMessage(
            `正在导入数据... ${progress}% (${importedCount}/${totalTransactions})`,
          );

          // 批量插入当前批次
          const batchSuccessCount = await databaseService.addTransactionsBatch(
            batchTransactions,
          );
          importedCount += batchSuccessCount;

          // 给UI一个更新的机会，避免阻塞
          if (batchIndex < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      } else {
        // 小数据量使用批量插入
        setLoadingMessage('正在导入数据...');
        importedCount = await databaseService.addTransactionsBatch(
          transactionsToImport,
        );
      }

      // 提取并保存备注历史
      setLoadingMessage('正在保存备注历史...');
      const noteStats = await extractAndSaveNoteHistory(transactionsToImport);

      // 显示成功提示
      let successMessage = `已导入 ${importedCount} 条记录`;
      if (newAccountBooksToCreate.size > 0) {
        successMessage += `，自动创建了 ${newAccountBooksToCreate.size} 个账本`;
      }
      if (noteStats.totalNotes > 0) {
        successMessage += `，保存了 ${noteStats.totalNotes} 条备注历史`;
      }
      showToast(successMessage, 'success');

      // 重新加载统计数据
      loadAccountStats();
    } catch (error) {
      console.error('导入数据失败', error);
      showToast('导入数据失败', 'error');
    } finally {
      // 隐藏加载弹窗
      setLoadingModalVisible(false);
    }
  };

  // 显示确认对话框的函数
  const showConfirmModal = (title, message, action) => {
    setConfirmModalTitle(title);
    setConfirmModalMessage(message);
    setConfirmModalAction(() => action);
    setConfirmModalVisible(true);
  };
  // 清空所有数据
  const handleRebase = async () => {
    showConfirmModal(
      '清空所有数据',
      '确定要清空所有数据吗？此操作将删除所有交易记录、分类、设置等数据且不可恢复！导出数据只针对账单数据导出，导出后可通过导入数据恢复账单数据，但其他数据将无法恢复',
      async () => {
        setIsLoading(true);
        try {
          await databaseService.resetDatabase();
          showToast('所有数据已清空', 'success');
          loadAccountStats(); // 重新加载账户统计
        } catch (error) {
          console.error('清空数据失败', error);
          showToast('清空数据失败', 'error');
        } finally {
          setIsLoading(false);
        }
      },
    );
  };
  // 添加清除账单函数
  const handleClearBillDatabase = async () => {
    showConfirmModal(
      '清除账单数据',
      '确定要清除账单数据吗？此操作将重置所有数据且不可恢复！务必先导出数据再进行操作',
      async () => {
        setIsLoading(true);
        try {
          await databaseService.resetBillDatabase();
          showToast('账单数据已清除', 'success');
          loadAccountStats(); // 重新加载账户统计
        } catch (error) {
          console.error('清除账单数据失败', error);
          showToast('清除账单数据失败', 'error');
        } finally {
          setIsLoading(false);
        }
      },
    );
  };

  // 添加清除数据库函数
  const handleClearDatabase = async () => {
    showConfirmModal(
      '清除所有数据',
      '确定要清除数据吗？此操作将重置所有数据且不可恢复！务必先导出数据再进行操作',
      async () => {
        setIsLoading(true);
        try {
          await databaseService.resetDatabase();
          showToast('数据已清除', 'success');
          loadAccountStats(); // 重新加载账户统计
        } catch (error) {
          console.error('清除数据失败', error);
          showToast('清除数据失败', 'error');
        } finally {
          setIsLoading(false);
        }
      },
    );
  };

  // 在 renderAutoRecordCard 函数中添加警告提示
  const renderAutoRecordCard = () => {
    return (
      <View style={styles.featureCard}>
        <View style={styles.featureHeader}>
          <View style={styles.featureIconContainer}>
            <Icon name="robot" size={20} color={COLORS.functional.error} />
          </View>
          <Text style={styles.featureTitle}>自动记账</Text>
        </View>

        <Text style={styles.featureDescription}>
          开启后可自动识别支付宝、微信支付，提醒记账
        </Text>

        {/* 添加提示信息 */}
        <View style={styles.warningContainer}>
          <Icon name="fire" size={14} color={COLORS.functional.warning} />
          <Text style={styles.warningText}>
            自动记账功能目前处于测试阶段，可能存在部分识别不准确的情况，请注意核对金额。
          </Text>
        </View>
      </View>
    );
  };

  // 加载预算设置
  const loadBudgetSettings = async () => {
    try {
      const settings = await databaseService.getBudgetSettings();
      if (settings) {
        setBudgetSettings(settings);
      }
    } catch (error) {
      console.error('加载预算设置失败', error);
    }
  };

  // 保存预算设置
  const saveBudgetSettings = async () => {
    try {
      await databaseService.saveBudgetSettings(budgetSettings);
      setBudgetModalVisible(false);
      showToast('预算设置已保存', 'success');
    } catch (error) {
      console.error('保存预算设置失败', error);
      showToast('保存预算设置失败', 'error');
    }
  };

  // 打开预算设置弹窗
  const openBudgetSettings = () => {
    setBudgetModalVisible(true);
  };

  // 处理预算设置开关变化
  const handleBudgetEnabledToggle = (value: boolean) => {
    const {daily = '', monthly = '', yearly = '', enabled} = budgetSettings;
    if (!daily && !monthly && !yearly && !enabled) {
      return showToast('请先设置预算金额', 'warning');
    }
    setBudgetSettings(prev => ({...prev, enabled: value}));
  };

  // 切换自动导出设置
  const toggleAutoExport = async (value?: boolean) => {
    try {
      // 如果没有传入值，则切换当前状态
      const newEnabled =
        value !== undefined ? value : !autoExportSettings.enabled;

      // 更新本地状态
      setAutoExportSettings(prev => ({
        ...prev,
        enabled: newEnabled,
      }));

      // 保存到数据库
      await databaseService.saveAutoExportSettings({
        ...autoExportSettings,
        enabled: newEnabled,
      });

      showToast(
        newEnabled ? '已开启自动导出功能' : '已关闭自动导出功能',
        'success',
      );
    } catch (error) {
      console.error('切换自动导出设置失败', error);
      showToast('设置失败', 'error');

      // 恢复原来的状态
      loadAutoExportSettings();
    }
  };

  // 渲染预算设置弹窗
  const renderBudgetSettingsModal = () => {
    return (
      <CustomModal
        visible={budgetModalVisible}
        onClose={() => setBudgetModalVisible(false)}>
        <View style={styles.budgetModalContent}>
          <Text style={styles.budgetModalTitle}>预算设置</Text>

          <View style={styles.budgetSwitchContainer}>
            <Text style={styles.budgetSwitchLabel}>启用预算提醒</Text>
            <AndSwitch
              checked={budgetSettings.enabled}
              onChange={handleBudgetEnabledToggle}
              color={colors.primary}
              style={styles.budgetSwitch}
            />
          </View>

          <View style={styles.budgetInputContainer}>
            <Text style={styles.budgetInputLabel}>每日预算</Text>
            <TextInput
              style={styles.budgetInput}
              placeholder="请输入每日预算金额"
              keyboardType="numeric"
              value={budgetSettings.daily}
              onChangeText={text =>
                setBudgetSettings(prev => ({...prev, daily: text}))
              }
            />
          </View>

          <View style={styles.budgetInputContainer}>
            <Text style={styles.budgetInputLabel}>每月预算</Text>
            <TextInput
              style={styles.budgetInput}
              placeholder="请输入每月预算金额"
              keyboardType="numeric"
              value={budgetSettings.monthly}
              onChangeText={text =>
                setBudgetSettings(prev => ({...prev, monthly: text}))
              }
            />
          </View>

          <View style={styles.budgetInputContainer}>
            <Text style={styles.budgetInputLabel}>每年预算</Text>
            <TextInput
              style={styles.budgetInput}
              placeholder="请输入每年预算金额"
              keyboardType="numeric"
              value={budgetSettings.yearly}
              onChangeText={text =>
                setBudgetSettings(prev => ({...prev, yearly: text}))
              }
            />
          </View>

          <View style={styles.budgetNote}>
            <Icon name="circle-info" size={14} color={COLORS.text.gray} />
            <Text style={styles.budgetNoteText}>
              当支出超过预设额度时，系统将会提醒您注意消费习惯，帮助您更好地管理财务。
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.budgetSaveButton, {backgroundColor: colors.primary}]}
            onPress={saveBudgetSettings}>
            <Text style={styles.budgetSaveButtonText}>保存设置</Text>
          </TouchableOpacity>
        </View>
      </CustomModal>
    );
  };

  // 添加加载弹窗组件
  const renderLoadingModal = () => (
    <Modal
      transparent={true}
      visible={loadingModalVisible}
      animationType="fade"
      onRequestClose={() => {}}>
      <View style={styles.loadingModalOverlay}>
        <View style={styles.loadingModalContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingModalText}>{loadingMessage}</Text>
        </View>
      </View>
    </Modal>
  );

  return (
    <PageContainer headerTitle="个人中心" backgroundColor={COLORS.secondary}>
      <ScrollView style={styles.container}>
        {/* 账户统计信息卡片 - 可折叠设计 */}
        <View style={styles.statsCard}>
          <View
            style={[styles.statsGradientBg, {backgroundColor: colors.primary}]}>
            {/* 统计头部 - 始终显示 */}
            <TouchableOpacity
              style={styles.statsHeader}
              onPress={() => setStatsExpanded(!statsExpanded)}
              activeOpacity={0.8}>
              <View style={styles.statsIconContainer}>
                <Icon name="chart-line" size={18} color="white" />
              </View>
              <Text style={styles.statsHeaderText}>账户统计</Text>
              <View style={styles.statsHeaderBadge}>
                <Text style={styles.statsHeaderBadgeText}>实时</Text>
              </View>
              <View style={styles.statsExpandIcon}>
                <Icon
                  name={statsExpanded ? 'chevron-up' : 'chevron-down'}
                  size={14}
                  color="rgba(255,255,255,0.8)"
                />
              </View>
            </TouchableOpacity>

            {/* 展开的详细内容 */}
            {statsExpanded && (
              <>
                {/* 主要统计数据 */}
                <View style={styles.mainStatsContainer}>
                  <View style={styles.mainStatsItem}>
                    <Text style={styles.mainStatsLabel}>记账天数</Text>
                    <Text style={styles.mainStatsValue}>
                      {accountStats.totalDays}
                    </Text>
                  </View>
                  <View style={styles.statsDivider} />
                  <View style={styles.mainStatsItem}>
                    <Text style={styles.mainStatsLabel}>记账笔数</Text>
                    <Text style={styles.mainStatsValue}>
                      {accountStats.totalRecords}
                    </Text>
                  </View>
                </View>

                {/* 次要统计数据 */}
                <View style={styles.secondaryStatsRow}>
                  <View style={styles.secondaryStatsItemCompact}>
                    <Icon
                      name="calendar-day"
                      size={14}
                      color="rgba(255,255,255,0.9)"
                    />
                    <Text style={styles.secondaryStatsTextCompact}>
                      {accountStats.todayDate}
                    </Text>
                  </View>

                  <View style={styles.secondaryStatsItemCompact}>
                    <Icon
                      name="clock"
                      size={14}
                      color="rgba(255,255,255,0.9)"
                    />
                    <Text style={styles.secondaryStatsTextCompact}>
                      首次 {accountStats.firstRecordDate}
                    </Text>
                  </View>
                </View>
              </>
            )}

            {/* 装饰性元素 */}
            <View style={styles.decorativeCircle1} />
            {statsExpanded && <View style={styles.decorativeCircle2} />}
          </View>
        </View>

        {/* 今日运势卡片 - 可折叠设计 */}
        {showFortune && fortuneData && (
          <View style={styles.fortuneCard}>
            <View style={styles.fortuneContent}>
              {/* 运势头部 - 始终显示 */}
              <TouchableOpacity
                style={styles.fortuneHeader}
                onPress={() => setFortuneExpanded(!fortuneExpanded)}
                activeOpacity={0.8}>
                <View style={styles.fortuneIconContainer}>
                  <Icon name="star" size={16} color="#FFD700" />
                </View>
                <Text style={styles.fortuneTitle}>今日运势很棒！</Text>
                <View style={styles.fortuneScoreBadge}>
                  <Text style={styles.fortuneScoreText}>
                    {fortuneData.overall}
                  </Text>
                </View>
                <View style={styles.fortuneExpandIcon}>
                  <Icon
                    name={fortuneExpanded ? 'chevron-up' : 'chevron-down'}
                    size={14}
                    color={colors.primary}
                  />
                </View>
              </TouchableOpacity>

              {/* 展开的详细内容 */}
              {fortuneExpanded && (
                <>
                  <View style={styles.fortuneDetails}>
                    <View style={styles.fortuneDetailItem}>
                      <Text style={styles.fortuneDetailLabel}>生肖</Text>
                      <Text style={styles.fortuneDetailValue}>
                        {fortuneData.zodiac.name}
                      </Text>
                    </View>
                    {fortuneData.constellation && (
                      <View style={styles.fortuneDetailItem}>
                        <Text style={styles.fortuneDetailLabel}>星座</Text>
                        <Text style={styles.fortuneDetailValue}>
                          {fortuneData.constellation.name}
                        </Text>
                      </View>
                    )}
                    <View style={styles.fortuneDetailItem}>
                      <Text style={styles.fortuneDetailLabel}>幸运数字</Text>
                      <Text style={styles.fortuneDetailValue}>
                        {fortuneData.luckyNumbers.slice(0, 3).join(', ')}
                      </Text>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.fortuneAction}
                    onPress={() => navigation.navigate('dailyFortune')}
                    activeOpacity={0.7}>
                    <Text style={styles.fortuneActionText}>
                      查看详细运势分析
                    </Text>
                    <Icon
                      name="chevron-right"
                      size={10}
                      color={colors.primary}
                    />
                  </TouchableOpacity>
                </>
              )}
            </View>

            {/* 装饰元素 */}
            <View style={styles.fortuneDecorative1} />
            {fortuneExpanded && <View style={styles.fortuneDecorative2} />}
          </View>
        )}

        {/* 功能列表 - 为各功能项添加独特的图标颜色 */}
        <View style={styles.functionCard}>
          {/* <View style={styles.functionHeader}>
            <Icon name="gear" size={22} color={colors.primary} />
            <Text style={styles.functionHeaderText}>功能</Text>
          </View> */}

          <View style={styles.gridContainer}>
            {/* 导出数据 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={handleExportData}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="file-export" size={22} color="#34C759" />
              </View>
              <Text style={styles.gridItemText}>导出账单</Text>
            </TouchableOpacity>

            {/* 导入数据 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => setImportSourceModalVisible(true)}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="file-import" size={22} color="#FF9500" />
              </View>
              <Text style={styles.gridItemText}>导入账单</Text>
            </TouchableOpacity>

            {/* 预算设置 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={openBudgetSettings}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="money-bill-trend-up" size={22} color="#AF52DE" />
              </View>
              <Text style={styles.gridItemText}>预算设置</Text>
              <Text style={styles.gridItemStatus}>
                {budgetSettings.enabled ? '已开启' : '未开启'}
              </Text>
            </TouchableOpacity>

            {/* 自动导出 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => toggleAutoExport()}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="calendar-check" size={22} color="#5AC8FA" />
              </View>
              <Text style={styles.gridItemText}>自动导出</Text>
              <Text style={styles.gridItemStatus}>
                {autoExportSettings.enabled ? '已开启' : '未开启'}
              </Text>
            </TouchableOpacity>

            {/* 信用卡还款提醒 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('creditCardList')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="credit-card" size={22} color="#5856D6" />
              </View>
              <Text style={styles.gridItemText}>信用卡还款</Text>
            </TouchableOpacity>

            {/* 分期账单管理 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('installmentPlans')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="money-bill" size={22} color="#FF3B30" />
              </View>
              <Text style={styles.gridItemText}>分期账单</Text>
            </TouchableOpacity>
            {/* 借款记录入口 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('loanRecords')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="money-check-dollar" size={22} color="#FFD60A" />
              </View>
              <Text style={styles.gridItemText}>借款记录</Text>
            </TouchableOpacity>

            {/* 我的账本入口 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('myAccountBooks')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="book" size={22} color="#32D74B" />
              </View>
              <Text style={styles.gridItemText}>我的账本</Text>
            </TouchableOpacity>

            {/* 家庭账单入口 */}
            {/* <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('familyMembers')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="users" size={22} color="#7E57C2" />
              </View>
              <Text style={styles.gridItemText}>家庭账单</Text>
            </TouchableOpacity> */}

            {/* 商品列表入口 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('productList')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="box" size={22} color="#007AFF" />
              </View>
              <Text style={styles.gridItemText}>愿望清单</Text>
            </TouchableOpacity>
            {/* 每日运势分析入口 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('dailyFortune')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="star" size={22} color="#FF2D55" />
              </View>
              <Text style={styles.gridItemText}>每日运势</Text>
            </TouchableOpacity>
            {/* 自动记账 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => setPermissionsModalVisible(true)}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="robot" size={22} color="#FFCC00" />
              </View>
              <Text style={styles.gridItemText}>自动记账</Text>
            </TouchableOpacity>
            {/* 主题设置 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={() => navigation.navigate('themeSettings')}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="palette" size={22} color="#007AFF" />
              </View>
              <Text style={styles.gridItemText}>主题设置</Text>
            </TouchableOpacity>
            {/* 数据备份 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={handleDataBackup}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="database" size={22} color="#34C759" />
              </View>
              <Text style={styles.gridItemText}>数据备份</Text>
            </TouchableOpacity>

            {/* 数据恢复 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={handleDataRestore}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon name="cloud-arrow-down" size={22} color="#007AFF" />
              </View>
              <Text style={styles.gridItemText}>数据恢复</Text>
            </TouchableOpacity>

            {/* 清空所有数据 */}
            <TouchableOpacity
              style={styles.gridItem}
              onPress={handleRebase}
              activeOpacity={0.7}>
              <View style={styles.iconBackground}>
                <Icon
                  name="trash-can"
                  size={22}
                  color={COLORS.functional.error}
                />
              </View>
              <Text style={styles.gridItemText}>清空所有数据</Text>
            </TouchableOpacity>
            {/* 清除账单数据 - 已隐藏，功能重复 */}
            {/* <TouchableOpacity
              style={styles.gridItem}
              onPress={handleClearBillDatabase}
              activeOpacity={0.7}>
              <View
                style={[
                  styles.iconBackground,
                  {backgroundColor: 'rgba(255, 59, 48, 0.1)'},
                ]}>
                <Icon name="trash" size={22} color={COLORS.functional.error} />
              </View>
              <Text style={styles.gridItemText}>清除账单数据</Text>
            </TouchableOpacity> */}

            {/* 清除所有数据 */}
            {/* <TouchableOpacity
              style={styles.gridItem}
              onPress={handleClearDatabase}
              activeOpacity={0.7}>
              <View
                style={[
                  styles.iconBackground,
                  {backgroundColor: 'rgba(255, 59, 48, 0.1)'},
                ]}>
                <Icon name="trash" size={22} color={COLORS.functional.error} />
              </View>
              <Text style={styles.gridItemText}>清除所有数据</Text>
            </TouchableOpacity> */}
            {/* 添加填充元素以确保总数为3的倍数 */}
            {[1, 2, 3].map((_, index) => (
              <View
                key={`filler-${index}`}
                style={[
                  styles.gridItem,
                  {opacity: 0}, // 使填充元素不可见
                ]}
              />
            ))}
          </View>
        </View>

        {/* 危险操作区域 - 改良样式 */}
        {/* <View style={styles.dangerCard}>
          <TouchableOpacity
            style={styles.dangerButton}
            onPress={handleClearDatabase}>
            <Icon
              name="trash-can"
              size={20}
              color="white"
              style={{marginRight: 8}}
            />
            <Text style={styles.dangerButtonText}>清除所有数据</Text>
          </TouchableOpacity>
        </View> */}
      </ScrollView>

      {/* 权限弹窗 */}
      <CustomModal
        visible={permissionsModalVisible}
        onClose={handleClosePermissionsModal}
        customStyle={styles.permissionsModal}>
        <View style={styles.permissionsContainer}>
          <Text style={styles.permissionsTitle}>自动记账设置</Text>
          <Text style={styles.permissionsDescription}>
            为了确保自动记账功能正常工作，请在后台锁定应用，避免被系统清理。
          </Text>
          <Text style={styles.permissionsDescription}>
            自动记账功能需要以下权限才能正常工作，请确保所有权限都已开启。
          </Text>

          {/* 无障碍权限 */}
          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Icon
                name="universal-access"
                size={20}
                color={colors.primary}
                style={styles.permissionIcon}
              />
              <View>
                <Text style={styles.permissionName}>无障碍服务</Text>
                <Text style={styles.permissionDesc}>
                  用于检测支付操作和金额
                </Text>
              </View>
            </View>
            <AndSwitch
              color={colors.primary}
              checked={accessibilityEnabled}
              onChange={handleAccessibilityToggle}
              style={styles.budgetSwitch}
            />
          </View>

          {/* 通知权限 */}
          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Icon
                name="bell"
                size={20}
                color={colors.primary}
                style={styles.permissionIcon}
              />
              <View>
                <Text style={styles.permissionName}>通知权限</Text>
                <Text style={styles.permissionDesc}>用于读取支付成功通知</Text>
              </View>
            </View>
            <AndSwitch
              color={colors.primary}
              checked={notificationEnabled}
              onChange={handleNotificationToggle}
              style={styles.budgetSwitch}
            />
          </View>

          {/* 悬浮窗权限 */}
          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Icon
                name="window-restore"
                size={20}
                color={colors.primary}
                style={styles.permissionIcon}
              />
              <View>
                <Text style={styles.permissionName}>悬浮窗权限</Text>
                <Text style={styles.permissionDesc}>用于显示支付确认弹窗</Text>
              </View>
            </View>
            <AndSwitch
              color={colors.primary}
              checked={overlayEnabled}
              onChange={handleOverlayToggle}
              style={styles.budgetSwitch}
            />
          </View>

          <Text style={styles.permissionsNote}>
            注意：开启这些权限仅用于本地识别支付信息，不会收集或上传任何数据。
          </Text>

          <TouchableOpacity
            style={[
              styles.permissionsDoneButton,
              {backgroundColor: colors.primary},
            ]}
            onPress={handleClosePermissionsModal}>
            <Text style={styles.permissionsDoneButtonText}>完成</Text>
          </TouchableOpacity>
        </View>
      </CustomModal>

      {/* 导入数据确认弹窗 */}
      <CustomModal
        visible={importDialogVisible}
        onClose={() => setImportDialogVisible(false)}
        customStyle={styles.confirmModal}>
        <View style={styles.confirmModalContent}>
          <Text style={styles.confirmModalTitle}>确认导入</Text>
          <Text style={styles.confirmModalMessage}>
            将导入 {importData?.transactions?.length || 0}{' '}
            条记录，记录可能会和现有数据重复，是否继续？
          </Text>
          <View style={styles.confirmModalButtons}>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalCancelButton,
              ]}
              onPress={() => {
                setImportDialogVisible(false);
                setImportData(null);
              }}>
              <Text style={styles.confirmModalCancelText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalConfirmButton,
                {backgroundColor: colors.primary},
              ]}
              onPress={confirmImportData}>
              <Text style={styles.confirmModalConfirmText}>确认导入</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>

      {/* 通用确认弹窗 */}
      <CustomModal
        visible={confirmModalVisible}
        onClose={() => setConfirmModalVisible(false)}
        customStyle={styles.confirmModal}>
        <View style={styles.confirmModalContent}>
          <Text style={styles.confirmModalTitle}>{confirmModalTitle}</Text>
          <Text style={styles.confirmModalMessage}>{confirmModalMessage}</Text>
          <View style={styles.confirmModalButtons}>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalCancelButton,
              ]}
              onPress={() => setConfirmModalVisible(false)}>
              <Text style={styles.confirmModalCancelText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalConfirmButton,
                {backgroundColor: colors.primary},
              ]}
              onPress={() => {
                setConfirmModalVisible(false);
                confirmModalAction();
              }}>
              <Text style={styles.confirmModalConfirmText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>

      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        confirmText="确定"
      />

      {/* 预算设置弹窗 */}
      {renderBudgetSettingsModal()}

      {/* 导入源选择弹窗 */}
      {renderImportSourceModal()}

      {/* 加载弹窗 */}
      {renderLoadingModal()}

      {/* 数据恢复确认对话框 */}
      <CustomModal
        visible={restoreConfirmVisible}
        onClose={() => setRestoreConfirmVisible(false)}
        customStyle={styles.confirmModal}>
        <View style={styles.confirmModalContent}>
          <Text style={styles.confirmModalTitle}>确认数据恢复</Text>
          <Text style={styles.confirmModalMessage}>
            将恢复备份文件中的其他数据，包括信用卡、借款记录、愿望清单等。
            {'\n\n'}
            注意：恢复过程中可能会创建重复的数据项，请确认是否继续？
            {'\n\n'}
            账单数据请使用"导入账单"功能。
          </Text>
          <View style={styles.confirmModalButtons}>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalCancelButton,
              ]}
              onPress={() => {
                setRestoreConfirmVisible(false);
                setRestoreData(null);
              }}>
              <Text style={styles.confirmModalCancelText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.confirmModalButton,
                styles.confirmModalConfirmButton,
              ]}
              onPress={confirmDataRestore}>
              <Text style={styles.confirmModalConfirmText}>确定恢复</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  budgetSwitch: {
    width: 40,
    height: 24,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  // 现代设计样式
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 10,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.background.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  userSubtitle: {
    fontSize: 14,
    color: COLORS.text.gray,
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.background.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statsOverview: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.text.gray,
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: COLORS.border.light,
    marginHorizontal: 20,
  },
  fortuneCardModern: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.white,
    marginHorizontal: 20,
    marginBottom: 10,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  fortuneIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 215, 0, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fortuneInfo: {
    flex: 1,
  },
  fortuneText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  fortuneScore: {
    fontSize: 14,
    color: COLORS.text.gray,
  },
  functionsContainer: {
    paddingHorizontal: 20,
  },
  functionGroup: {
    marginBottom: 20,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 12,
    marginLeft: 4,
  },
  functionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  functionItem: {
    width: '33.33%',
    paddingHorizontal: 6,
    marginBottom: 12,
  },
  functionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.background.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  functionText: {
    fontSize: 12,
    color: COLORS.text.primary,
    textAlign: 'center',
    fontWeight: '500',
  },
  functionStatus: {
    fontSize: 10,
    color: COLORS.text.gray,
    textAlign: 'center',
    marginTop: 2,
  },
  statsCard: {
    borderRadius: 20,
    margin: 16,
    marginBottom: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },
  statsGradientBg: {
    padding: 18,
    position: 'relative',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  statsIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  statsHeaderText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    flex: 1,
  },
  statsHeaderBadge: {
    backgroundColor: 'rgba(255,255,255,0.25)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  statsHeaderBadgeText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
  },
  statsExpandIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  mainStatsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    marginBottom: 16,
    paddingVertical: 14,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  mainStatsItem: {
    alignItems: 'center',
    flex: 1,
  },
  mainStatsLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 6,
    fontWeight: '500',
  },
  mainStatsValue: {
    fontSize: 26,
    fontWeight: '800',
    color: 'white',
    lineHeight: 30,
  },
  mainStatsUnit: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.9)',
    marginTop: 2,
    fontWeight: '600',
  },
  statsDivider: {
    width: 1,
    height: 50,
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: 16,
  },
  secondaryStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.15)',
  },
  secondaryStatsItemCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.08)',
    padding: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    flex: 1,
    marginHorizontal: 4,
  },
  secondaryStatsTextCompact: {
    fontSize: 11,
    color: 'rgba(255,255,255,0.9)',
    marginLeft: 6,
    fontWeight: '500',
    flex: 1,
  },
  decorativeCircle1: {
    position: 'absolute',
    top: -20,
    right: -20,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: -25,
    left: -25,
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255,255,255,0.03)',
  },
  // 运势卡片样式
  fortuneCard: {
    borderRadius: 16,
    margin: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
    overflow: 'hidden',
    position: 'relative',
  },
  fortuneContent: {
    padding: 14,
    backgroundColor: COLORS.background.white,
    position: 'relative',
  },
  fortuneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  fortuneIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.background.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  fortuneTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    flex: 1,
  },
  fortuneScoreBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
    marginRight: 8,
  },
  fortuneScoreText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '700',
  },
  fortuneExpandIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.background.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fortuneDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    backgroundColor: COLORS.background.gray,
    borderRadius: 10,
    paddingHorizontal: 12,
  },
  fortuneDetailItem: {
    alignItems: 'center',
    flex: 1,
  },
  fortuneDetailLabel: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 4,
    fontWeight: '500',
  },
  fortuneDetailValue: {
    fontSize: 11,
    color: COLORS.text.primary,
    fontWeight: '600',
  },
  fortuneAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: COLORS.background.light,
    borderRadius: 8,
  },
  fortuneActionText: {
    fontSize: 11,
    color: COLORS.text.primary,
    marginRight: 6,
    fontWeight: '500',
  },
  fortuneDecorative1: {
    position: 'absolute',
    top: -15,
    right: -15,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  fortuneDecorative2: {
    position: 'absolute',
    bottom: -20,
    left: -20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.03)',
  },
  functionCard: {
    backgroundColor: COLORS.background.white,
    borderRadius: 20,
    padding: 20,
    margin: 16,
    marginTop: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 4,
  },
  functionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  functionHeaderText: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 10,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    paddingTop: 10,
  },
  gridItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 10,
    marginHorizontal: '1.65%',
    padding: 5,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  gridItemText: {
    fontSize: 14,
    color: COLORS.text.primary,
    marginTop: 8,
    fontWeight: '500',
    textAlign: 'center',
  },
  gridItemStatus: {
    fontSize: 12,
    color: COLORS.text.gray,
    marginTop: 2,
    textAlign: 'center',
  },
  iconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
    // 玻璃样式效果 - 兼容iOS和Android
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    // 只保留iOS阴影，移除Android elevation
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  dangerCard: {
    borderRadius: 16,
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 32,
  },
  dangerButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.9)',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF3B30',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  dangerButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
  },
  permissionsModal: {
    padding: 0,
    borderRadius: 20,
    width: '90%',
    maxWidth: 360,
    overflow: 'hidden',
  },
  permissionsContainer: {
    padding: 24,
  },
  permissionsTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  permissionsDescription: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 10,
    textAlign: 'center',
    lineHeight: 22,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  permissionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  permissionIcon: {
    marginRight: 14,
    width: 24,
    textAlign: 'center',
  },
  permissionName: {
    fontSize: 17,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  permissionDesc: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginTop: 3,
  },
  permissionsNote: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginTop: 24,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  permissionsDoneButton: {
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  permissionsDoneButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  settingText: {
    fontSize: 17,
    color: COLORS.text.primary,
  },
  section: {
    padding: 20,
    backgroundColor: COLORS.background.white,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 14,
  },
  confirmModal: {
    padding: 0,
    borderRadius: 20,
    width: '90%',
    maxWidth: 360,
    overflow: 'hidden',
  },
  confirmModalContent: {
    padding: 24,
  },
  confirmModalTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  confirmModalMessage: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmModalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  confirmModalCancelButton: {
    backgroundColor: '#F2F2F7',
  },
  confirmModalConfirmButton: {},
  confirmModalCancelText: {
    color: COLORS.text.primary,
    fontSize: 17,
    fontWeight: '500',
  },
  confirmModalConfirmText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 204, 0, 0.1)',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginTop: 8,
  },
  warningText: {
    fontSize: 13,
    color: COLORS.functional.warning,
    marginLeft: 10,
    flex: 1,
    lineHeight: 18,
  },
  featureCard: {
    backgroundColor: COLORS.background.white,
    borderRadius: 16,
    padding: 0,
    width: '100%',
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  featureTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  featureDescription: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 12,
    lineHeight: 20,
  },
  budgetModalContent: {
    padding: 0,
  },
  budgetModalTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  budgetSwitchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
    paddingBottom: 18,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  budgetSwitchLabel: {
    fontSize: 17,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  budgetInputContainer: {
    marginBottom: 18,
  },
  budgetInputLabel: {
    fontSize: 15,
    color: COLORS.text.primary,
    marginBottom: 10,
    fontWeight: '500',
  },
  budgetInput: {
    borderWidth: 0.5,
    borderColor: '#E5E5EA',
    borderRadius: 10,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 17,
    backgroundColor: '#F9F9FB',
  },
  budgetNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    padding: 14,
    marginTop: 8,
    marginBottom: 24,
  },
  budgetNoteText: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginLeft: 10,
    flex: 1,
    lineHeight: 20,
  },
  budgetSaveButton: {
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  budgetSaveButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
  },
  importSourceModalContainer: {
    padding: 24,
  },
  importSourceModalTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 10,
    textAlign: 'center',
  },
  importSourceModalSubtitle: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  importSourceOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 14,
    marginBottom: 14,
  },
  importSourceIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  importSourceTextContainer: {
    flex: 1,
  },
  importSourceName: {
    fontSize: 17,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  importSourceDescription: {
    fontSize: 13,
    color: COLORS.text.gray,
    lineHeight: 18,
  },
  loadingModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingModalContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    width: '85%',
    maxWidth: 320,
  },
  loadingModalText: {
    marginTop: 16,
    fontSize: 17,
    color: COLORS.text.primary,
    textAlign: 'center',
  },
  dangerSeparator: {
    height: 12,
  },
  warningButton: {
    backgroundColor: 'rgba(255, 149, 0, 0.9)', // 使用警告色，区分于删除按钮
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  menuItemIcon: {
    marginRight: 14,
    width: 24,
    textAlign: 'center',
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemText: {
    fontSize: 17,
    color: COLORS.text.primary,
  },
});
export default ProfileScreen;
