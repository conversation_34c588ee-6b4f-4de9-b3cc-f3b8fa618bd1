// import {Notifications} from 'react-native-notifications';
import notifee, {
  TimestampTrigger,
  TriggerType,
  RepeatFrequency,
} from '@notifee/react-native';
import databaseService, {CreditCard} from './DatabaseService';
import dayjs from 'dayjs';
import {writeFile} from '../utils/utils';

class NotificationService {
  constructor() {
    this.initialize();
  }

  // 检查并执行自动导出
  async checkAndAutoExport() {
    try {
      const shouldExport = await databaseService.shouldAutoExport();

      if (shouldExport) {
        console.log('开始执行自动导出...');
        // 获取所有交易记录
        const transactions = await databaseService.getAllTransactions();
        // 创建 CSV 内容
        let csvContent = '日期,类型,金额,分类,家庭账单id,家庭账单,账本id,账本,备注\n';

        // 添加交易数据
        transactions.forEach(transaction => {
          const type = transaction.type === 'expense' ? '支出' : '收入';
          const date = transaction.date
            ? dayjs(transaction.date).format('YYYY-MM-DD')
            : '';
          const amount = transaction.amount || 0;
          const categoryName = transaction.categoryName || '未知分类';
          const note = transaction.note || '';
          const familyName = transaction.familyName || '';
          const familyId = transaction.familyId || '';
          const accountBookId = transaction.accountBookId || 1;
          const accountBookName = transaction.accountBookName || '默认账本';

          // 处理 CSV 中的特殊字符
          const escapedNote = note.replace(/"/g, '""');
          csvContent += `${date},${type},${amount},${categoryName},${familyId},${familyName},${accountBookId},${accountBookName},"${escapedNote}"\n`;
        });

        // 分享文件
        await writeFile(csvContent, '雪球记账');

        // 更新最后导出日期
        await databaseService.updateLastExportDate();

        // 发送通知
        this.scheduleLocalNotification({
          title: '自动导出完成',
          body: '交易记录已自动导出',
          id: 'auto_export',
          timestamp: Date.now(),
          channelName: '自动导出通知',
        });

        console.log('自动导出完成');
      }
    } catch (error) {
      console.error('自动导出失败:', error);
    }
  }

  // 初始化通知服务
  async initialize() {
    // 请求通知权限
    await notifee.requestPermission();
  }

  // 处理通知点击
  handleNotificationClick(payload) {
    notifee.displayNotification({
      title: payload.title || '',
      android: {
        channelId: payload.channelId || '',
        pressAction: {
          id: payload.pressActionId || 'default',
        },
      },
    });
  }

  // 安排定时本地通知
  async scheduleLocalNotification({
    title,
    body,
    id,
    timestamp,
    channelName,
    repeatFrequency = RepeatFrequency.NONE,
  }) {
    // Create a time-based trigger
    const trigger: TimestampTrigger = {
      type: TriggerType.TIMESTAMP,
      timestamp,
      repeatFrequency,
      // alarmManager: true,
    };
    // Create a channel (required for Android)
    const channelId = await notifee.createChannel({
      id,
      name: channelName,
    });

    // Create a trigger notification
    await notifee.createTriggerNotification(
      {
        title,
        body,
        android: {
          channelId: channelId,
          pressAction: {
            id: 'default',
          },
        },
      },
      trigger,
    );
  }

  // 生成唯一标识符
  generateIdentifier(cardId, type, days = 0) {
    return `card_${cardId}_${type}_${days}_${Date.now()}`;
  }

  // 每日提醒记账
  async scheduleDailyReminders() {
    // 发送即时通知
    this.scheduleLocalNotification({
      title: '每日记账通知',
      body: '今天不要忘记进行记账哦！💰',
      id: '10086',
      timestamp: dayjs().hour(12).minute(0).second(0).valueOf(),
      channelName: '每日记账提醒',
      repeatFrequency: RepeatFrequency.DAILY,
    });
  }

  // 测试通知
  async testNotification() {
    // 发送即时通知
    this.scheduleLocalNotification({
      title: '测试通知',
      body: '这是一条测试通知，验证通知功能是否正常',
      id: '1',
      timestamp: Date.now() + 10000,
      channelName: '测试通知',
    });
    console.log('测试通知已发送');
    return true;
  }

  // 安排信用卡还款提醒
  async scheduleCreditCardReminders() {
    // 重新安排则取消之前的通知
    const ids = await notifee.getTriggerNotificationIds();
    await Promise.all(ids.map(id => notifee.cancelTriggerNotification(id)));
    try {
      // 获取所有信用卡
      const cards = await databaseService.getAllCreditCards();
      if (!cards || cards.length === 0) {
        return;
      }

      console.log(`开始为 ${cards.length} 张信用卡安排提醒`, cards);

      // 为每张卡安排提醒
      for (const card of cards) {
        this.scheduleRemindersForCard(card);
      }
    } catch (error) {
      console.error('安排信用卡提醒失败:', error);
    }
  }

  // 为单张信用卡安排提醒
  async scheduleRemindersForCard(card: CreditCard) {
    try {
      const {id, bankName, billingDay, paymentDueDay} = card;
      // 账单日当天提醒
      // 如果账单日大于当前日期，则将提醒时间戳设置为账单日当天上午09:00:00
      // 如果账单日小于当前日期，则将提醒时间戳设置为账单日+1个月当天上午09:00:00
      const billingTimestamp =
        dayjs().date() <= billingDay
          ? dayjs().date(billingDay).hour(9).minute(0).second(0).valueOf()
          : dayjs()
              .add(1, 'month')
              .date(billingDay)
              .hour(9)
              .minute(0)
              .second(0)
              .valueOf();
      this.scheduleLocalNotification({
        title: '信用卡账单日提醒',
        body: `今天是您${bankName}信用卡的账单日，本期账单已生成`,
        id: '2',
        timestamp: billingTimestamp,
        channelName: '信用卡账单日提醒',
      });
      // 还款日当天提醒
      // 如果还款日大于当前日期，则将提醒时间戳设置为还款日当天上午09:00:00
      // 如果还款日小于当前日期，则将提醒时间戳设置为还款日+1个月当天上午09:00:00
      const paymentDueTimestamp =
        dayjs().date() <= paymentDueDay
          ? dayjs().date(paymentDueDay).hour(9).minute(0).second(0).valueOf()
          : dayjs()
              .add(1, 'month')
              .date(paymentDueDay)
              .hour(9)
              .minute(0)
              .second(0)
              .valueOf();
      if (dayjs().date() === paymentDueDay) {
        this.scheduleLocalNotification({
          title: '信用卡还款提醒',
          body: `今天是您${bankName}信用卡的最后还款日，请务必完成还款`,
          id: '3',
          timestamp: paymentDueTimestamp,
          channelName: '信用卡还款提醒',
        });
      }
    } catch (error) {
      console.error(`为信用卡${card.id}安排提醒失败:`, error);
    }
  }
}

export default new NotificationService();
